#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# Import config
import config

def test_password_input():
    """Test cải tiến nhập mật khẩu"""
    print("🚀 Bắt đầu test cải tiến nhập mật khẩu...")
    
    # Khởi tạo Chrome driver
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    
    if config.HEADLESS_MODE:
        chrome_options.add_argument("--headless")
    
    service = Service(config.CHROME_DRIVER_PATH)
    driver = webdriver.Chrome(service=service, options=chrome_options)
    
    try:
        # Mở trang web
        print("🌐 Đang mở trang web...")
        driver.get(config.WEBSITE_URL)
        time.sleep(3)
        
        # Đăng nhập
        print("🔐 Đang đăng nhập...")
        username_input = driver.find_element(By.NAME, "username")
        password_input = driver.find_element(By.NAME, "password")
        
        username_input.clear()
        username_input.send_keys(config.USERNAME)
        
        password_input.clear()
        password_input.send_keys(config.PASSWORD)
        
        # Click nút đăng nhập
        login_button = driver.find_element(By.XPATH, "//button[contains(text(), 'Đăng nhập')]")
        driver.execute_script("arguments[0].click();", login_button)
        
        # Chờ đăng nhập thành công
        time.sleep(5)
        
        # Tìm trang thiết lập mật khẩu rút tiền
        print("🔍 Tìm trang thiết lập mật khẩu rút tiền...")
        
        # Tìm các input field mật khẩu
        password_selectors = [
            "input[type='password']",
            "input[placeholder*='mật khẩu']",
            "input[placeholder*='password']",
            ".password-input",
            "[class*='password']",
            "input[maxlength='1']",  # Ô nhập từng ký tự
            "input[pattern='[0-9]']"  # Ô nhập số
        ]
        
        password_inputs = []
        for selector in password_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        password_inputs.append(element)
            except:
                continue
        
        print(f"🔢 Tìm thấy {len(password_inputs)} input field")
        
        if len(password_inputs) >= 12:
            # Test nhập mật khẩu với cải tiến mới
            password = "121212"
            print(f"🔐 Test nhập mật khẩu: {password}")
            
            # Nhập vào 6 ô đầu tiên
            print("📝 Nhập vào 6 ô đầu tiên (Sửa Đổi):")
            for i, digit in enumerate(password):
                if i < 6:
                    input_field = password_inputs[i]
                    
                    # Test cải tiến nhập
                    success = test_improved_input(driver, input_field, digit, f"Sửa Đổi {i+1}")
                    
                    if success:
                        print(f"  ✅ Ô {i+1}: {digit} - Thành công")
                    else:
                        print(f"  ❌ Ô {i+1}: {digit} - Thất bại")
            
            # Nhập vào 6 ô tiếp theo
            print("📝 Nhập vào 6 ô tiếp theo (Xác Nhận):")
            for i, digit in enumerate(password):
                if i < 6:
                    input_field = password_inputs[i + 6]
                    
                    # Test cải tiến nhập
                    success = test_improved_input(driver, input_field, digit, f"Xác Nhận {i+1}")
                    
                    if success:
                        print(f"  ✅ Ô {i+1}: {digit} - Thành công")
                    else:
                        print(f"  ❌ Ô {i+1}: {digit} - Thất bại")
            
            # Chụp screenshot kết quả
            driver.save_screenshot("screenshots/test_password_input_result.png")
            print("📸 Đã chụp screenshot kết quả")
            
        else:
            print("❌ Không tìm thấy đủ input field để test")
        
    except Exception as e:
        print(f"❌ Lỗi trong quá trình test: {str(e)}")
        driver.save_screenshot("screenshots/test_password_input_error.png")
    
    finally:
        print("🔚 Đóng trình duyệt...")
        driver.quit()

def test_improved_input(driver, input_field, digit, label):
    """Test cải tiến nhập input với nhiều phương pháp"""
    success = False
    
    try:
        # Cách 1: Click + Clear + Send keys
        try:
            input_field.click()
            time.sleep(0.1)
            input_field.clear()
            input_field.send_keys(digit)
            
            # Kiểm tra đã nhập thành công chưa
            current_value = input_field.get_attribute('value')
            if current_value == digit:
                success = True
                print(f"    {label}: Cách 1 thành công")
        except Exception as e:
            print(f"    {label}: Cách 1 thất bại - {e}")
        
        # Cách 2: JavaScript nếu cách 1 thất bại
        if not success:
            try:
                driver.execute_script("arguments[0].focus();", input_field)
                driver.execute_script("arguments[0].value = arguments[1];", input_field, digit)
                # Trigger events để UI cập nhật
                driver.execute_script("arguments[0].dispatchEvent(new Event('input', { bubbles: true }));", input_field)
                driver.execute_script("arguments[0].dispatchEvent(new Event('change', { bubbles: true }));", input_field)
                
                # Kiểm tra lại
                current_value = input_field.get_attribute('value')
                if current_value == digit:
                    success = True
                    print(f"    {label}: Cách 2 thành công")
            except Exception as e:
                print(f"    {label}: Cách 2 thất bại - {e}")
        
        # Cách 3: Send keys trực tiếp nếu vẫn thất bại
        if not success:
            try:
                input_field.send_keys(digit)
                
                # Kiểm tra lại
                current_value = input_field.get_attribute('value')
                if current_value == digit:
                    success = True
                    print(f"    {label}: Cách 3 thành công")
            except Exception as e:
                print(f"    {label}: Cách 3 thất bại - {e}")
        
        # Hiển thị giá trị cuối cùng
        final_value = input_field.get_attribute('value')
        print(f"    {label}: Giá trị cuối cùng = '{final_value}'")
        
    except Exception as e:
        print(f"    {label}: Lỗi tổng quát - {e}")
    
    return success

if __name__ == "__main__":
    test_password_input()
