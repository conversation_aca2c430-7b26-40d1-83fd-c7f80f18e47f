#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
import config

def test_visual_input():
    """Test visual input với nhiều phương pháp"""
    print("🚀 Test visual input với cải tiến mới...")
    
    # Khởi tạo Chrome driver
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--window-size=1920,1080")
    
    service = Service("chromedriver.exe")
    driver = webdriver.Chrome(service=service, options=chrome_options)
    
    try:
        # Mở trang test
        print("🌐 Đang mở trang web...")
        driver.get("https://www.13win16.com/home/<USER>")
        time.sleep(3)
        
        # <PERSON><PERSON><PERSON> nhập nhanh
        print("🔐 Đăng nhập nhanh...")
        try:
            username_input = driver.find_element(By.CSS_SELECTOR, "input[placeholder*='Nhập Số điện thoại']")
            password_input = driver.find_element(By.CSS_SELECTOR, "input[placeholder='Mật khẩu']")
            login_button = driver.find_element(By.CSS_SELECTOR, "button.ui-button.ui-button--primary")
            
            username_input.send_keys("user1220")
            password_input.send_keys("tranhan572")
            login_button.click()
            
            time.sleep(5)
            print("✅ Đăng nhập thành công")
        except Exception as e:
            print(f"❌ Lỗi đăng nhập: {e}")
            return
        
        # Chuyển đến trang thiết lập mật khẩu
        print("🔐 Chuyển đến trang thiết lập mật khẩu...")
        security_url = "https://www.13win16.com/home/<USER>"
        driver.get(security_url)
        time.sleep(3)
        
        # Tìm input fields
        print("🔍 Tìm input fields...")
        password_inputs = driver.find_elements(By.CSS_SELECTOR, "[class*='password']")
        print(f"✅ Tìm thấy {len(password_inputs)} input fields")
        
        if len(password_inputs) >= 12:
            # Test nhập với visual feedback
            password = "121212"
            print(f"\n🎯 Test nhập mật khẩu: {password}")
            print("=" * 50)
            
            # Test 6 ô đầu tiên
            for i, digit in enumerate(password):
                if i < 6:
                    input_field = password_inputs[i]
                    print(f"\n📝 Test ô {i+1}:")
                    
                    success = test_improved_input_visual(driver, input_field, digit, f"Ô {i+1}")
                    
                    if success:
                        print(f"  ✅ Thành công!")
                    else:
                        print(f"  ❌ Thất bại!")
                    
                    time.sleep(1)  # Delay để quan sát
            
            print("\n🎯 Kết quả cuối cùng:")
            print("=" * 30)
            
            # Kiểm tra tất cả các ô
            for i in range(6):
                try:
                    value = password_inputs[i].get_attribute('value') or ""
                    expected = password[i]
                    status = "✅" if value == expected else "❌"
                    print(f"  {status} Ô {i+1}: '{expected}' → '{value}'")
                except:
                    print(f"  ❌ Ô {i+1}: Lỗi đọc giá trị")
            
            # Chụp screenshot cuối cùng
            driver.save_screenshot("screenshots/test_visual_input_final.png")
            print("\n📸 Đã chụp screenshot: test_visual_input_final.png")
            
            # Giữ trình duyệt mở để quan sát
            print("\n⏳ Giữ trình duyệt mở 10 giây để quan sát...")
            time.sleep(10)
            
        else:
            print("❌ Không tìm thấy đủ input fields")
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        driver.save_screenshot("screenshots/test_visual_input_error.png")
    
    finally:
        print("🔚 Đóng trình duyệt...")
        driver.quit()

def test_improved_input_visual(driver, input_field, digit, label):
    """Test cải tiến nhập input với visual feedback"""
    print(f"    🎯 {label}: Nhập '{digit}'")
    
    success = False
    
    # Cách 1: Click + Clear + Send keys
    try:
        print("      🔸 Cách 1: Click + Clear + Send keys")
        input_field.click()
        time.sleep(0.1)
        input_field.clear()
        input_field.send_keys(digit)
        
        # Kiểm tra
        current_value = input_field.get_attribute('value')
        if current_value == digit:
            success = True
            print(f"      ✅ Thành công: '{current_value}'")
        else:
            print(f"      ❌ Thất bại: '{current_value}' != '{digit}'")
    except Exception as e:
        print(f"      ❌ Lỗi: {e}")
    
    # Cách 2: JavaScript nếu cách 1 thất bại
    if not success:
        try:
            print("      🔸 Cách 2: JavaScript + Events")
            driver.execute_script("arguments[0].focus();", input_field)
            driver.execute_script("arguments[0].value = arguments[1];", input_field, digit)
            # Trigger events
            driver.execute_script("arguments[0].dispatchEvent(new Event('input', { bubbles: true }));", input_field)
            driver.execute_script("arguments[0].dispatchEvent(new Event('change', { bubbles: true }));", input_field)
            driver.execute_script("arguments[0].dispatchEvent(new Event('keyup', { bubbles: true }));", input_field)
            
            # Kiểm tra
            current_value = input_field.get_attribute('value')
            if current_value == digit:
                success = True
                print(f"      ✅ Thành công: '{current_value}'")
            else:
                print(f"      ❌ Thất bại: '{current_value}' != '{digit}'")
        except Exception as e:
            print(f"      ❌ Lỗi: {e}")
    
    # Cách 3: Force visual update
    if not success:
        try:
            print("      🔸 Cách 3: Force Visual Update")
            # Highlight element
            driver.execute_script("""
                arguments[0].focus();
                arguments[0].style.backgroundColor = 'yellow';
                arguments[0].style.border = '3px solid red';
            """, input_field)
            time.sleep(0.5)
            
            # Simulate typing
            input_field.clear()
            input_field.click()
            for char in digit:
                input_field.send_keys(char)
                time.sleep(0.1)
            
            # Force refresh
            driver.execute_script("arguments[0].blur(); arguments[0].focus();", input_field)
            
            # Kiểm tra
            current_value = input_field.get_attribute('value')
            if current_value == digit:
                success = True
                print(f"      ✅ Thành công: '{current_value}'")
            else:
                print(f"      ❌ Thất bại: '{current_value}' != '{digit}'")
        except Exception as e:
            print(f"      ❌ Lỗi: {e}")
    
    # Debug info
    try:
        is_visible = input_field.is_displayed()
        is_enabled = input_field.is_enabled()
        element_text = input_field.text
        print(f"      🔍 Debug: visible={is_visible}, enabled={is_enabled}, text='{element_text}'")
    except:
        pass
    
    return success

if __name__ == "__main__":
    test_visual_input()
