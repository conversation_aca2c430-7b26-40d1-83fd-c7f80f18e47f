"""
Script để khắc phục lỗi Chrome driver trên Windows
"""

import os
import sys
import subprocess
import requests
import zipfile
import shutil
from pathlib import Path

def check_chrome_version():
    """Kiểm tra phiên bản Chrome"""
    try:
        # Thử các đường dẫn Chrome phổ biến trên Windows
        chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.getenv('USERNAME'))
        ]

        for chrome_path in chrome_paths:
            if os.path.exists(chrome_path):
                print(f"Tìm thấy Chrome tại: {chrome_path}")

                # Thử lấy phiên bản từ registry trước
                try:
                    import winreg
                    key_paths = [
                        r"SOFTWARE\Google\Chrome\BLBeacon",
                        r"SOFTWARE\WOW6432Node\Google\Chrome\BLBeacon",
                        r"SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\chrome.exe"
                    ]

                    for key_path in key_paths:
                        try:
                            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, key_path) as key:
                                version, _ = winreg.QueryValueEx(key, "version")
                                print(f"Phiên bản Chrome (từ registry): {version}")
                                major_version = version.split('.')[0]
                                return major_version
                        except:
                            continue

                except ImportError:
                    pass

                # Nếu không lấy được từ registry, thử command line
                try:
                    result = subprocess.run([chrome_path, "--version"],
                                          capture_output=True, text=True, timeout=5)
                    version = result.stdout.strip()
                    print(f"Phiên bản Chrome: {version}")

                    # Trích xuất số phiên bản
                    version_number = version.split()[-1]
                    major_version = version_number.split('.')[0]
                    return major_version

                except Exception as e:
                    print(f"Không thể lấy phiên bản Chrome qua command: {e}")

                # Fallback: sử dụng phiên bản mặc định
                print("Sử dụng phiên bản Chrome mặc định: 120")
                return "120"

        print("Không tìm thấy Chrome browser!")
        return None

    except Exception as e:
        print(f"Lỗi kiểm tra Chrome: {e}")
        # Fallback: sử dụng phiên bản mặc định
        print("Sử dụng phiên bản Chrome mặc định: 120")
        return "120"

def download_chromedriver(version):
    """Tải Chrome driver phù hợp"""
    try:
        print(f"Đang tải Chrome driver...")

        # Sử dụng API mới cho tất cả phiên bản
        try:
            print("Lấy thông tin phiên bản driver mới nhất...")
            api_url = "https://googlechromelabs.github.io/chrome-for-testing/last-known-good-versions-with-downloads.json"
            response = requests.get(api_url, timeout=15)
            data = response.json()
            driver_version = data['channels']['Stable']['version']
            print(f"Phiên bản driver: {driver_version}")

            # URL tải driver cho Windows 64-bit
            download_url = f"https://storage.googleapis.com/chrome-for-testing-public/{driver_version}/win64/chromedriver-win64.zip"

        except Exception as api_error:
            print(f"Lỗi API mới: {api_error}")
            # Fallback: sử dụng phiên bản cố định
            driver_version = "120.0.6099.109"
            download_url = f"https://storage.googleapis.com/chrome-for-testing-public/{driver_version}/win64/chromedriver-win64.zip"
            print(f"Sử dụng phiên bản fallback: {driver_version}")

        print(f"Đang tải từ: {download_url}")

        # Tải file với retry
        max_retries = 3
        for attempt in range(max_retries):
            try:
                response = requests.get(download_url, timeout=60)
                response.raise_for_status()
                break
            except Exception as e:
                print(f"Lần thử {attempt + 1} thất bại: {e}")
                if attempt == max_retries - 1:
                    # Thử URL backup
                    print("Thử URL backup...")
                    backup_urls = [
                        f"https://chromedriver.storage.googleapis.com/120.0.6099.109/chromedriver_win32.zip",
                        "https://github.com/SeleniumHQ/selenium/releases/download/selenium-4.15.0/chromedriver_win32.zip"
                    ]

                    for backup_url in backup_urls:
                        try:
                            print(f"Thử tải từ: {backup_url}")
                            response = requests.get(backup_url, timeout=60)
                            response.raise_for_status()
                            driver_version = "120.0.6099.109"
                            break
                        except:
                            continue
                    else:
                        raise Exception("Không thể tải từ bất kỳ URL nào")

        # Lưu file zip
        zip_path = "chromedriver.zip"
        with open(zip_path, "wb") as f:
            f.write(response.content)

        print("Đã tải xong Chrome driver")
        return zip_path, driver_version

    except Exception as e:
        print(f"Lỗi tải Chrome driver: {e}")
        return None, None

def extract_and_install_driver(zip_path, driver_version):
    """Giải nén và cài đặt driver"""
    try:
        print("Đang giải nén Chrome driver...")
        
        # Tạo thư mục driver
        driver_dir = "chromedriver"
        if os.path.exists(driver_dir):
            shutil.rmtree(driver_dir)
        os.makedirs(driver_dir)
        
        # Giải nén
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(driver_dir)
        
        # Tìm file chromedriver.exe
        driver_exe = None
        for root, dirs, files in os.walk(driver_dir):
            for file in files:
                if file == "chromedriver.exe":
                    driver_exe = os.path.join(root, file)
                    break
            if driver_exe:
                break
        
        if not driver_exe:
            print("Không tìm thấy chromedriver.exe trong file zip")
            return False
        
        # Copy driver vào thư mục hiện tại
        final_driver_path = "chromedriver.exe"
        shutil.copy2(driver_exe, final_driver_path)
        
        # Thêm quyền thực thi
        os.chmod(final_driver_path, 0o755)
        
        print(f"Đã cài đặt Chrome driver tại: {os.path.abspath(final_driver_path)}")
        
        # Dọn dẹp
        os.remove(zip_path)
        shutil.rmtree(driver_dir)
        
        return True
        
    except Exception as e:
        print(f"Lỗi cài đặt driver: {e}")
        return False

def clear_driver_cache():
    """Xóa cache driver cũ"""
    try:
        cache_dirs = [
            os.path.join(os.path.expanduser("~"), ".wdm"),
            os.path.join(os.path.expanduser("~"), ".cache", "selenium"),
            os.path.join(os.path.expanduser("~"), "AppData", "Local", "Temp", "scoped_dir*")
        ]
        
        for cache_dir in cache_dirs:
            if os.path.exists(cache_dir):
                shutil.rmtree(cache_dir)
                print(f"Đã xóa cache: {cache_dir}")
                
    except Exception as e:
        print(f"Lỗi xóa cache: {e}")

def test_driver():
    """Test Chrome driver"""
    try:
        print("Đang test Chrome driver...")
        
        from selenium import webdriver
        from selenium.webdriver.chrome.service import Service
        from selenium.webdriver.chrome.options import Options
        
        # Thiết lập options
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        
        # Sử dụng driver local
        service = Service("./chromedriver.exe")
        
        # Khởi tạo driver
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # Test
        driver.get("https://www.google.com")
        title = driver.title
        print(f"Test thành công! Title: {title}")
        
        driver.quit()
        return True
        
    except Exception as e:
        print(f"Test driver thất bại: {e}")
        return False

def main():
    """Hàm main"""
    print("=== KHẮC PHỤC LỖI CHROME DRIVER ===")
    print()
    
    # 1. Kiểm tra Chrome
    print("1. Kiểm tra Chrome browser...")
    chrome_version = check_chrome_version()
    
    if not chrome_version:
        print("❌ Vui lòng cài đặt Google Chrome trước!")
        print("Tải tại: https://www.google.com/chrome/")
        return
    
    print(f"✅ Chrome version: {chrome_version}")
    print()
    
    # 2. Xóa cache cũ
    print("2. Xóa cache driver cũ...")
    clear_driver_cache()
    print("✅ Đã xóa cache")
    print()
    
    # 3. Tải driver mới
    print("3. Tải Chrome driver...")
    zip_path, driver_version = download_chromedriver(chrome_version)
    
    if not zip_path:
        print("❌ Không thể tải Chrome driver!")
        return
    
    print(f"✅ Đã tải driver version: {driver_version}")
    print()
    
    # 4. Cài đặt driver
    print("4. Cài đặt Chrome driver...")
    if not extract_and_install_driver(zip_path, driver_version):
        print("❌ Không thể cài đặt driver!")
        return
    
    print("✅ Đã cài đặt driver")
    print()
    
    # 5. Test driver
    print("5. Test Chrome driver...")
    if test_driver():
        print("✅ Chrome driver hoạt động tốt!")
        print()
        print("🎉 KHẮC PHỤC THÀNH CÔNG!")
        print("Bây giờ bạn có thể chạy lại: python main.py")
    else:
        print("❌ Driver vẫn chưa hoạt động!")
        print("Vui lòng:")
        print("- Chạy với quyền Administrator")
        print("- Tắt antivirus tạm thời")
        print("- Cập nhật Chrome lên phiên bản mới nhất")

if __name__ == "__main__":
    main()
