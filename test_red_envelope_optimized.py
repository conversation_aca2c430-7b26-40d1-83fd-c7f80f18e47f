#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script cho tính năng mở hồng bao tối ưu hóa
Kiểm tra logic timeout và tìm nút X nhanh chóng
"""

import time
from auto_login import AutoLogin
import config

def test_red_envelope_optimized():
    """Test tính năng mở hồng bao với logic tối ưu hóa"""
    
    print("🧪 Test tính năng mở hồng bao tối ưu hóa")
    print("=" * 50)
    
    # Thông tin test
    username = "hoangans39"
    password = "Hoangan123"
    withdrawal_password = "121212"
    
    print(f"👤 Username: {username}")
    print(f"🔐 Password: {password}")
    print(f"💰 Withdrawal Password: {withdrawal_password}")
    print(f"⏰ Red Envelope Timeout: {config.RED_ENVELOPE_TIMEOUT}s")
    print()
    
    # Khởi tạo AutoLogin
    auto_login = AutoLogin()
    
    try:
        # Đăng nhập
        print("🔑 Bước 1: Đăng nhập...")
        if not auto_login.perform_login(username, password, remember_me=True):
            print("❌ Đăng nhập thất bại!")
            return False
        
        print("✅ Đăng nhập thành công!")
        print()
        
        # Chuyển đến trang security
        print("🔗 Bước 2: Chuyển đến trang security...")
        security_url = "https://www.13win16.com/home/<USER>"
        print(f"🧧 Đang chuyển hướng đến trang security...")
        auto_login.driver.get(security_url)
        time.sleep(3)
        print()
        
        # Test mở hồng bao với logic tối ưu hóa
        print("🧧 Bước 3: Test mở hồng bao tối ưu hóa...")
        print("ℹ️ Logic mới:")
        print(f"  - Tìm hồng bao trong {config.RED_ENVELOPE_TIMEOUT}s")
        print("  - Tìm nút X trong 10s với selector tối ưu")
        print("  - Click nhanh và chắc chắn chuyển đến trang mật khẩu")
        print()
        
        start_time = time.time()
        
        if auto_login.open_red_envelope(withdrawal_password):
            elapsed_time = int(time.time() - start_time)
            print(f"✅ Quá trình hoàn tất trong {elapsed_time}s!")
            print("ℹ️ Kiểm tra log ở trên để xem chi tiết:")
            print("  - Có thể đã mở hồng bao thành công")
            print("  - Hoặc tài khoản không có hồng bao")
            print("  - Đã thiết lập mật khẩu rút tiền")
        else:
            print("❌ Có lỗi xảy ra trong quá trình thực hiện")
        
        print()
        print("📸 Screenshots đã tạo:")
        print("  - before_red_envelope.png")
        print("  - after_open_envelope.png (nếu có hồng bao)")
        print("  - after_close_popup.png (nếu đóng popup thành công)")
        print("  - after_force_close.png (nếu dùng debug mode)")
        print("  - withdrawal_password_page.png")
        print("  - after_confirm_password.png")
        
        # Chờ để xem kết quả
        input("\nNhấn Enter để đóng trình duyệt...")
        
        return True
        
    except Exception as e:
        print(f"❌ Lỗi trong quá trình test: {str(e)}")
        return False
        
    finally:
        # Đóng trình duyệt
        try:
            auto_login.close_driver()
            print("🔒 Đã đóng trình duyệt")
        except:
            pass

if __name__ == "__main__":
    print("🚀 Bắt đầu test tính năng mở hồng bao tối ưu hóa...")
    print()
    
    success = test_red_envelope_optimized()
    
    print()
    print("=" * 50)
    if success:
        print("✅ Test hoàn tất!")
    else:
        print("❌ Test thất bại!")
    
    print("🔍 Kiểm tra screenshots trong thư mục 'screenshots' để debug")
    print("📝 Kiểm tra logs để xem chi tiết quá trình thực hiện")
