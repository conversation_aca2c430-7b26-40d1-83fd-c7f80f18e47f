"""
Tool tự động đăng nhập và liên kết ngân hàng cho 13win.com
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import json
import os
from datetime import datetime
import sys

from auto_login import AutoLogin
from bank_linking import BankLinking
import config

class BankingTool:
    def __init__(self, root):
        self.root = root
        self.root.title("Tool Tự Động Đăng Nhập & Liên <PERSON>t Ngân Hàng - 13win.com")
        self.root.geometry("800x700")
        self.root.resizable(True, True)
        
        # Khởi tạo các biến
        self.auto_login = AutoLogin()
        self.bank_linking = None
        self.is_logged_in = False
        
        # Tạo giao diện
        self.create_widgets()
        
        # Load cấu hình đã lưu
        self.load_saved_config()
    
    def create_widgets(self):
        """Tạo giao diện ngườ<PERSON> dùng"""
        
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="Tool Tự Động 13win.com", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # Login Section
        login_frame = ttk.LabelFrame(main_frame, text="Thông Tin Đăng Nhập", padding="10")
        login_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        login_frame.columnconfigure(1, weight=1)
        
        # Username
        ttk.Label(login_frame, text="Tên đăng nhập:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.username_var = tk.StringVar()
        self.username_entry = ttk.Entry(login_frame, textvariable=self.username_var, width=30)
        self.username_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=5)
        
        # Password
        ttk.Label(login_frame, text="Mật khẩu:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.password_var = tk.StringVar()
        self.password_entry = ttk.Entry(login_frame, textvariable=self.password_var, 
                                       show="*", width=30)
        self.password_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=5)
        
        # Remember me
        self.remember_var = tk.BooleanVar(value=True)
        self.remember_check = ttk.Checkbutton(login_frame, text="Ghi nhớ đăng nhập", 
                                             variable=self.remember_var)
        self.remember_check.grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # Headless mode
        self.headless_var = tk.BooleanVar(value=False)
        self.headless_check = ttk.Checkbutton(login_frame, text="Chạy ẩn (không hiện trình duyệt)",
                                             variable=self.headless_var)
        self.headless_check.grid(row=3, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        # Withdrawal password
        ttk.Label(login_frame, text="Mật khẩu rút tiền:").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.withdrawal_password_var = tk.StringVar(value="121212")
        self.withdrawal_password_entry = ttk.Entry(login_frame, textvariable=self.withdrawal_password_var,
                                                  show="*", width=30)
        self.withdrawal_password_entry.grid(row=4, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=5)

        # Login button
        self.login_button = ttk.Button(login_frame, text="Đăng Nhập",
                                      command=self.start_login_thread)
        self.login_button.grid(row=5, column=0, columnspan=2, pady=10)
        
        # Bank Section
        bank_frame = ttk.LabelFrame(main_frame, text="Liên Kết Ngân Hàng", padding="10")
        bank_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        bank_frame.columnconfigure(1, weight=1)
        
        # Bank selection
        ttk.Label(bank_frame, text="Chọn ngân hàng:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.bank_var = tk.StringVar()
        self.bank_combo = ttk.Combobox(bank_frame, textvariable=self.bank_var, 
                                      values=config.SUPPORTED_BANKS, state="readonly")
        self.bank_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=5)
        
        # Account number
        ttk.Label(bank_frame, text="Số tài khoản:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.account_var = tk.StringVar()
        self.account_entry = ttk.Entry(bank_frame, textvariable=self.account_var, width=30)
        self.account_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=5)
        
        # Account name
        ttk.Label(bank_frame, text="Tên tài khoản:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.account_name_var = tk.StringVar()
        self.account_name_entry = ttk.Entry(bank_frame, textvariable=self.account_name_var, width=30)
        self.account_name_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=5)
        
        # Bank branch
        ttk.Label(bank_frame, text="Chi nhánh (tùy chọn):").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.branch_var = tk.StringVar()
        self.branch_entry = ttk.Entry(bank_frame, textvariable=self.branch_var, width=30)
        self.branch_entry.grid(row=3, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=5)
        
        # Bank buttons
        button_frame = ttk.Frame(bank_frame)
        button_frame.grid(row=4, column=0, columnspan=2, pady=10)
        
        self.link_bank_button = ttk.Button(button_frame, text="Liên Kết Ngân Hàng", 
                                          command=self.start_bank_linking_thread, state="disabled")
        self.link_bank_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.check_banks_button = ttk.Button(button_frame, text="Kiểm Tra Ngân Hàng Đã Liên Kết", 
                                           command=self.check_linked_banks, state="disabled")
        self.check_banks_button.pack(side=tk.LEFT)
        
        # Control buttons
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=3, column=0, columnspan=2, pady=10)

        self.save_config_button = ttk.Button(control_frame, text="Lưu Cấu Hình",
                                           command=self.save_config)
        self.save_config_button.pack(side=tk.LEFT, padx=(0, 10))

        self.red_envelope_button = ttk.Button(control_frame, text="🧧 Mở Hồng Bao",
                                            command=self.start_red_envelope_thread, state="disabled")
        self.red_envelope_button.pack(side=tk.LEFT, padx=(0, 10))

        self.screenshot_button = ttk.Button(control_frame, text="Chụp Màn Hình",
                                          command=self.take_screenshot, state="disabled")
        self.screenshot_button.pack(side=tk.LEFT, padx=(0, 10))

        self.close_button = ttk.Button(control_frame, text="Đóng Trình Duyệt",
                                     command=self.close_browser, state="disabled")
        self.close_button.pack(side=tk.LEFT)
        
        # Log area
        log_frame = ttk.LabelFrame(main_frame, text="Nhật Ký Hoạt Động", padding="10")
        log_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(4, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Clear log button
        self.clear_log_button = ttk.Button(log_frame, text="Xóa Log", command=self.clear_log)
        self.clear_log_button.grid(row=1, column=0, pady=(5, 0))
    
    def log_message(self, message):
        """Ghi log vào text area"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
        print(message)  # In ra console
    
    def clear_log(self):
        """Xóa log"""
        self.log_text.delete(1.0, tk.END)
    
    def start_login_thread(self):
        """Bắt đầu thread đăng nhập"""
        if not self.username_var.get() or not self.password_var.get():
            messagebox.showerror("Lỗi", "Vui lòng nhập đầy đủ tên đăng nhập và mật khẩu!")
            return
        
        self.login_button.config(state="disabled")
        thread = threading.Thread(target=self.perform_login)
        thread.daemon = True
        thread.start()
    
    def perform_login(self):
        """Thực hiện đăng nhập"""
        try:
            self.log_message("Bắt đầu quá trình đăng nhập...")
            
            # Setup driver
            if not self.auto_login.setup_driver(headless=self.headless_var.get()):
                self.log_message("Lỗi: Không thể khởi tạo trình duyệt")
                self.login_button.config(state="normal")
                return
            
            # Navigate to login page
            if not self.auto_login.navigate_to_login():
                self.log_message("Lỗi: Không thể truy cập trang đăng nhập")
                self.auto_login.close_driver()
                self.login_button.config(state="normal")
                return
            
            # Perform login
            success = self.auto_login.perform_login(
                self.username_var.get(),
                self.password_var.get(),
                self.remember_var.get()
            )
            
            if success:
                self.log_message("Đăng nhập thành công!")
                self.is_logged_in = True
                self.bank_linking = BankLinking(self.auto_login.driver)
                
                # Enable bank buttons
                self.link_bank_button.config(state="normal")
                self.check_banks_button.config(state="normal")
                self.red_envelope_button.config(state="normal")
                self.screenshot_button.config(state="normal")
                self.close_button.config(state="normal")
                
                messagebox.showinfo("Thành công", "Đăng nhập thành công!")
            else:
                self.log_message("Đăng nhập thất bại!")
                self.auto_login.close_driver()
                messagebox.showerror("Lỗi", "Đăng nhập thất bại! Vui lòng kiểm tra lại thông tin.")
            
        except Exception as e:
            self.log_message(f"Lỗi: {str(e)}")
            messagebox.showerror("Lỗi", f"Có lỗi xảy ra: {str(e)}")
        finally:
            self.login_button.config(state="normal")
    
    def start_bank_linking_thread(self):
        """Bắt đầu thread liên kết ngân hàng"""
        if not self.is_logged_in:
            messagebox.showerror("Lỗi", "Vui lòng đăng nhập trước!")
            return
        
        if not all([self.bank_var.get(), self.account_var.get(), self.account_name_var.get()]):
            messagebox.showerror("Lỗi", "Vui lòng điền đầy đủ thông tin ngân hàng!")
            return
        
        self.link_bank_button.config(state="disabled")
        thread = threading.Thread(target=self.perform_bank_linking)
        thread.daemon = True
        thread.start()
    
    def perform_bank_linking(self):
        """Thực hiện liên kết ngân hàng"""
        try:
            self.log_message("Bắt đầu liên kết ngân hàng...")
            
            # Navigate to bank section
            if not self.bank_linking.navigate_to_bank_section():
                self.log_message("Không tìm thấy phần liên kết ngân hàng")
                return
            
            # Select bank
            if not self.bank_linking.select_bank(self.bank_var.get()):
                self.log_message(f"Không thể chọn ngân hàng {self.bank_var.get()}")
                return
            
            # Fill bank info
            if not self.bank_linking.fill_bank_info(
                self.account_var.get(),
                self.account_name_var.get(),
                self.branch_var.get()
            ):
                self.log_message("Không thể điền thông tin ngân hàng")
                return
            
            # Submit
            if not self.bank_linking.submit_bank_info():
                self.log_message("Không thể submit thông tin")
                return
            
            # Verify
            if self.bank_linking.verify_bank_linking():
                self.log_message("Liên kết ngân hàng thành công!")
                messagebox.showinfo("Thành công", "Liên kết ngân hàng thành công!")
            else:
                self.log_message("Liên kết ngân hàng có thể chưa thành công")
                messagebox.showwarning("Cảnh báo", "Không thể xác nhận kết quả liên kết")
            
        except Exception as e:
            self.log_message(f"Lỗi khi liên kết ngân hàng: {str(e)}")
            messagebox.showerror("Lỗi", f"Có lỗi xảy ra: {str(e)}")
        finally:
            self.link_bank_button.config(state="normal")
    
    def check_linked_banks(self):
        """Kiểm tra ngân hàng đã liên kết"""
        if not self.is_logged_in:
            messagebox.showerror("Lỗi", "Vui lòng đăng nhập trước!")
            return

        try:
            linked_banks = self.bank_linking.get_linked_banks()
            if linked_banks:
                banks_text = "\n".join(linked_banks)
                self.log_message(f"Ngân hàng đã liên kết:\n{banks_text}")
                messagebox.showinfo("Ngân hàng đã liên kết", banks_text)
            else:
                self.log_message("Chưa có ngân hàng nào được liên kết")
                messagebox.showinfo("Thông báo", "Chưa có ngân hàng nào được liên kết")

        except Exception as e:
            self.log_message(f"Lỗi khi kiểm tra ngân hàng: {str(e)}")
            messagebox.showerror("Lỗi", f"Có lỗi xảy ra: {str(e)}")

    def start_red_envelope_thread(self):
        """Bắt đầu thread mở hồng bao"""
        if not self.is_logged_in:
            messagebox.showerror("Lỗi", "Vui lòng đăng nhập trước!")
            return

        self.red_envelope_button.config(state="disabled")
        thread = threading.Thread(target=self.perform_red_envelope_opening)
        thread.daemon = True
        thread.start()

    def perform_red_envelope_opening(self):
        """Thực hiện mở hồng bao"""
        try:
            self.log_message("🧧 Bắt đầu quá trình mở hồng bao...")

            # Chuyển hướng đến trang security
            security_url = "https://www.13win16.com/home/<USER>"
            self.log_message(f"Đang chuyển hướng đến: {security_url}")
            self.auto_login.driver.get(security_url)

            # Thực hiện mở hồng bao
            withdrawal_password = self.withdrawal_password_var.get()
            if self.auto_login.open_red_envelope(withdrawal_password):
                self.log_message("✅ Đã mở hồng bao thành công!")
                messagebox.showinfo("Thành công", "Đã mở hồng bao thành công!")
            else:
                self.log_message("❌ Không thể mở hồng bao")
                messagebox.showerror("Lỗi", "Không thể mở hồng bao. Vui lòng kiểm tra lại!")

        except Exception as e:
            self.log_message(f"❌ Lỗi khi mở hồng bao: {str(e)}")
            messagebox.showerror("Lỗi", f"Có lỗi xảy ra khi mở hồng bao: {str(e)}")
        finally:
            self.red_envelope_button.config(state="normal")
    
    def take_screenshot(self):
        """Chụp màn hình"""
        if not self.is_logged_in:
            messagebox.showerror("Lỗi", "Vui lòng đăng nhập trước!")
            return
        
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"screenshot_{timestamp}.png"
            filepath = self.auto_login.take_screenshot(filename)
            
            if filepath:
                self.log_message(f"Đã chụp màn hình: {filepath}")
                messagebox.showinfo("Thành công", f"Đã lưu screenshot: {filepath}")
            else:
                messagebox.showerror("Lỗi", "Không thể chụp màn hình")
                
        except Exception as e:
            self.log_message(f"Lỗi khi chụp màn hình: {str(e)}")
            messagebox.showerror("Lỗi", f"Có lỗi xảy ra: {str(e)}")
    
    def close_browser(self):
        """Đóng trình duyệt"""
        try:
            self.auto_login.close_driver()
            self.is_logged_in = False
            
            # Disable buttons
            self.link_bank_button.config(state="disabled")
            self.check_banks_button.config(state="disabled")
            self.red_envelope_button.config(state="disabled")
            self.screenshot_button.config(state="disabled")
            self.close_button.config(state="disabled")
            
            self.log_message("Đã đóng trình duyệt")
            
        except Exception as e:
            self.log_message(f"Lỗi khi đóng trình duyệt: {str(e)}")
    
    def save_config(self):
        """Lưu cấu hình"""
        try:
            config_data = {
                "username": self.username_var.get(),
                "remember_login": self.remember_var.get(),
                "headless_mode": self.headless_var.get(),
                "withdrawal_password": self.withdrawal_password_var.get(),
                "bank": self.bank_var.get(),
                "account_name": self.account_name_var.get(),
                "branch": self.branch_var.get()
            }
            
            if not os.path.exists(config.DATA_DIR):
                os.makedirs(config.DATA_DIR)
            
            config_file = os.path.join(config.DATA_DIR, "config.json")
            with open(config_file, "w", encoding="utf-8") as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
            
            self.log_message("Đã lưu cấu hình")
            messagebox.showinfo("Thành công", "Đã lưu cấu hình thành công!")
            
        except Exception as e:
            self.log_message(f"Lỗi khi lưu cấu hình: {str(e)}")
            messagebox.showerror("Lỗi", f"Không thể lưu cấu hình: {str(e)}")
    
    def load_saved_config(self):
        """Load cấu hình đã lưu"""
        try:
            config_file = os.path.join(config.DATA_DIR, "config.json")
            if os.path.exists(config_file):
                with open(config_file, "r", encoding="utf-8") as f:
                    config_data = json.load(f)
                
                self.username_var.set(config_data.get("username", ""))
                self.remember_var.set(config_data.get("remember_login", True))
                self.headless_var.set(config_data.get("headless_mode", False))
                self.withdrawal_password_var.set(config_data.get("withdrawal_password", "121212"))
                self.bank_var.set(config_data.get("bank", ""))
                self.account_name_var.set(config_data.get("account_name", ""))
                self.branch_var.set(config_data.get("branch", ""))
                
                self.log_message("Đã load cấu hình đã lưu")
                
        except Exception as e:
            self.log_message(f"Không thể load cấu hình: {str(e)}")
    
    def on_closing(self):
        """Xử lý khi đóng ứng dụng"""
        try:
            if self.is_logged_in:
                self.auto_login.close_driver()
            self.root.destroy()
        except Exception:
            pass

def main():
    """Hàm main"""
    try:
        # Tạo thư mục cần thiết
        for directory in [config.DATA_DIR, config.SCREENSHOTS_DIR, config.LOGS_DIR]:
            if not os.path.exists(directory):
                os.makedirs(directory)
        
        # Khởi tạo GUI
        root = tk.Tk()
        app = BankingTool(root)
        
        # Xử lý đóng ứng dụng
        root.protocol("WM_DELETE_WINDOW", app.on_closing)
        
        # Chạy ứng dụng
        root.mainloop()
        
    except Exception as e:
        print(f"Lỗi khởi tạo ứng dụng: {str(e)}")
        messagebox.showerror("Lỗi", f"Không thể khởi tạo ứng dụng: {str(e)}")

if __name__ == "__main__":
    main()
