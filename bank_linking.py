"""
Module xử lý liên kết ngân hàng
"""

import time
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import config

class BankLinking:
    def __init__(self, driver):
        self.driver = driver
        self.wait = WebDriverWait(driver, config.ELEMENT_WAIT_TIMEOUT)
    
    def navigate_to_bank_section(self):
        """Điều hướng đến phần liên kết ngân hàng"""
        try:
            print("Đang tìm phần liên kết ngân hàng...")
            
            # Tìm các link có thể dẫn đến phần ngân hàng
            bank_links = [
                "a[href*='bank']",
                "a[href*='payment']",
                "a[href*='deposit']",
                "a[href*='withdraw']",
                ".menu-item:contains('<PERSON>ân hàng')",
                ".nav-item:contains('Thanh toán')"
            ]
            
            for link_selector in bank_links:
                try:
                    if "contains" in link_selector:
                        # Sử dụng XPath cho text contains
                        element = self.driver.find_element(By.XPATH, f"//a[contains(text(), 'Ngân hàng')] | //a[contains(text(), 'Thanh toán')]")
                    else:
                        element = self.driver.find_element(By.CSS_SELECTOR, link_selector)
                    
                    element.click()
                    time.sleep(2)
                    print("Đã truy cập phần ngân hàng")
                    return True
                    
                except NoSuchElementException:
                    continue
            
            print("Không tìm thấy link đến phần ngân hàng")
            return False
            
        except Exception as e:
            print(f"Lỗi khi điều hướng đến phần ngân hàng: {str(e)}")
            return False
    
    def find_bank_options(self):
        """Tìm các tùy chọn ngân hàng"""
        try:
            bank_elements = []
            
            # Các selector có thể chứa danh sách ngân hàng
            bank_selectors = [
                ".bank-option",
                ".payment-method",
                ".bank-item",
                ".bank-list li",
                ".payment-option",
                "img[alt*='bank']",
                "img[src*='bank']"
            ]
            
            for selector in bank_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        bank_elements.extend(elements)
                except NoSuchElementException:
                    continue
            
            return bank_elements
            
        except Exception as e:
            print(f"Lỗi khi tìm tùy chọn ngân hàng: {str(e)}")
            return []
    
    def select_bank(self, bank_name):
        """Chọn ngân hàng cụ thể"""
        try:
            print(f"Đang chọn ngân hàng: {bank_name}")
            
            # Tìm element chứa tên ngân hàng
            bank_selectors = [
                f"img[alt*='{bank_name}']",
                f"img[src*='{bank_name.lower()}']",
                f"*[title*='{bank_name}']",
                f"//*[contains(text(), '{bank_name}')]"
            ]
            
            for selector in bank_selectors:
                try:
                    if selector.startswith("//"):
                        element = self.driver.find_element(By.XPATH, selector)
                    else:
                        element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    
                    element.click()
                    time.sleep(2)
                    print(f"Đã chọn ngân hàng {bank_name}")
                    return True
                    
                except NoSuchElementException:
                    continue
            
            print(f"Không tìm thấy ngân hàng {bank_name}")
            return False
            
        except Exception as e:
            print(f"Lỗi khi chọn ngân hàng: {str(e)}")
            return False
    
    def fill_bank_info(self, account_number, account_name, bank_branch=""):
        """Điền thông tin tài khoản ngân hàng"""
        try:
            print("Đang điền thông tin tài khoản...")
            
            # Tìm và điền số tài khoản
            account_selectors = [
                "input[name*='account']",
                "input[placeholder*='account']",
                "input[placeholder*='số tài khoản']",
                "input[id*='account']"
            ]
            
            for selector in account_selectors:
                try:
                    account_input = self.driver.find_element(By.CSS_SELECTOR, selector)
                    account_input.clear()
                    account_input.send_keys(account_number)
                    print("Đã điền số tài khoản")
                    break
                except NoSuchElementException:
                    continue
            
            # Tìm và điền tên tài khoản
            name_selectors = [
                "input[name*='name']",
                "input[placeholder*='name']",
                "input[placeholder*='tên']",
                "input[id*='name']"
            ]
            
            for selector in name_selectors:
                try:
                    name_input = self.driver.find_element(By.CSS_SELECTOR, selector)
                    name_input.clear()
                    name_input.send_keys(account_name)
                    print("Đã điền tên tài khoản")
                    break
                except NoSuchElementException:
                    continue
            
            # Điền chi nhánh nếu có
            if bank_branch:
                branch_selectors = [
                    "input[name*='branch']",
                    "input[placeholder*='branch']",
                    "input[placeholder*='chi nhánh']",
                    "select[name*='branch']"
                ]
                
                for selector in branch_selectors:
                    try:
                        branch_input = self.driver.find_element(By.CSS_SELECTOR, selector)
                        if branch_input.tag_name == "select":
                            # Xử lý dropdown
                            from selenium.webdriver.support.ui import Select
                            select = Select(branch_input)
                            select.select_by_visible_text(bank_branch)
                        else:
                            branch_input.clear()
                            branch_input.send_keys(bank_branch)
                        print("Đã điền chi nhánh")
                        break
                    except NoSuchElementException:
                        continue
            
            return True
            
        except Exception as e:
            print(f"Lỗi khi điền thông tin ngân hàng: {str(e)}")
            return False
    
    def submit_bank_info(self):
        """Submit thông tin ngân hàng"""
        try:
            print("Đang submit thông tin ngân hàng...")
            
            # Tìm button submit
            submit_selectors = [
                "button[type='submit']",
                "input[type='submit']",
                ".btn-submit",
                ".submit-btn",
                "button:contains('Xác nhận')",
                "button:contains('Lưu')"
            ]
            
            for selector in submit_selectors:
                try:
                    if "contains" in selector:
                        element = self.driver.find_element(By.XPATH, f"//button[contains(text(), 'Xác nhận')] | //button[contains(text(), 'Lưu')]")
                    else:
                        element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    
                    element.click()
                    time.sleep(3)
                    print("Đã submit thông tin ngân hàng")
                    return True
                    
                except NoSuchElementException:
                    continue
            
            print("Không tìm thấy button submit")
            return False
            
        except Exception as e:
            print(f"Lỗi khi submit thông tin: {str(e)}")
            return False
    
    def verify_bank_linking(self):
        """Kiểm tra liên kết ngân hàng thành công"""
        try:
            # Tìm các dấu hiệu thành công
            success_indicators = [
                ".success-message",
                ".alert-success",
                "*[class*='success']",
                "//*[contains(text(), 'thành công')]",
                "//*[contains(text(), 'hoàn tất')]"
            ]
            
            for indicator in success_indicators:
                try:
                    if indicator.startswith("//"):
                        self.driver.find_element(By.XPATH, indicator)
                    else:
                        self.driver.find_element(By.CSS_SELECTOR, indicator)
                    print("Liên kết ngân hàng thành công!")
                    return True
                except NoSuchElementException:
                    continue
            
            return False
            
        except Exception as e:
            print(f"Lỗi khi kiểm tra kết quả: {str(e)}")
            return False
    
    def get_linked_banks(self):
        """Lấy danh sách ngân hàng đã liên kết"""
        try:
            linked_banks = []
            
            # Tìm danh sách ngân hàng đã liên kết
            bank_list_selectors = [
                ".linked-bank",
                ".bank-account",
                ".payment-method.active",
                ".bank-info"
            ]
            
            for selector in bank_list_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        bank_info = element.text.strip()
                        if bank_info:
                            linked_banks.append(bank_info)
                except NoSuchElementException:
                    continue
            
            return linked_banks
            
        except Exception as e:
            print(f"Lỗi khi lấy danh sách ngân hàng: {str(e)}")
            return []
