"""
Test Chrome driver đơn giản
"""

import os
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options

def test_local_driver():
    """Test driver local"""
    try:
        print("Testing local Chrome driver...")
        
        # <PERSON><PERSON><PERSON> tra file tồn tại
        driver_path = "./chromedriver.exe"
        if not os.path.exists(driver_path):
            print(f"❌ File không tồn tại: {driver_path}")
            return False
        
        print(f"✅ File tồn tại: {driver_path}")
        
        # Thiết lập options
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        
        # Thiết lập service
        service = Service(driver_path)
        
        # Khởi tạo driver
        print("Khởi tạo Chrome driver...")
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # Test
        print("<PERSON><PERSON><PERSON> cập Google...")
        driver.get("https://www.google.com")
        title = driver.title
        print(f"✅ Thành công! Title: {title}")
        
        # Đóng driver
        driver.quit()
        print("✅ Driver hoạt động tốt!")
        return True
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return False

def test_auto_login():
    """Test module auto_login"""
    try:
        print("\nTesting auto_login module...")
        
        from auto_login import AutoLogin
        
        auto_login = AutoLogin()
        
        # Test setup driver
        if auto_login.setup_driver(headless=True):
            print("✅ AutoLogin setup driver thành công!")
            
            # Test navigate
            if auto_login.navigate_to_login():
                print("✅ Navigate to login thành công!")
            else:
                print("❌ Navigate to login thất bại!")
            
            # Đóng driver
            auto_login.close_driver()
            return True
        else:
            print("❌ AutoLogin setup driver thất bại!")
            return False
            
    except Exception as e:
        print(f"❌ Lỗi test auto_login: {e}")
        return False

if __name__ == "__main__":
    print("=== TEST CHROME DRIVER ===")
    
    # Test 1: Driver local
    if test_local_driver():
        print("\n=== TEST AUTO_LOGIN MODULE ===")
        # Test 2: Module auto_login
        test_auto_login()
    
    print("\n=== KẾT THÚC TEST ===")
