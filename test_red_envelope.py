"""
Script test tính năng mở hồng bao
"""

import time
from auto_login import AutoLogin

def test_red_envelope():
    """Test tính năng mở hồng bao"""
    print("🧧 Test tính năng mở hồng bao")
    print("=" * 50)
    
    # Nhập thông tin đăng nhập
    username = input("Nhập tên đăng nhập: ").strip()
    password = input("Nhập mật khẩu: ").strip()
    
    if not username or not password:
        print("❌ Vui lòng nhập đầy đủ thông tin!")
        return
    
    auto_login = AutoLogin()
    
    try:
        # Setup driver
        print("🔧 Đang khởi tạo trình duyệt...")
        if not auto_login.setup_driver(headless=False):
            print("❌ Không thể khởi tạo trình duyệt")
            return
        
        # Navigate to login page
        print("🌐 Đang truy cập trang đăng nhập...")
        if not auto_login.navigate_to_login():
            print("❌ Không thể truy cập trang đăng nhập")
            return
        
        # Perform login
        print("🔐 Đang đăng nhập...")
        if not auto_login.perform_login(username, password, True):
            print("❌ Đăng nhập thất bại")
            return
        
        print("✅ Đăng nhập thành công!")
        
        # Chờ một chút để đảm bảo đăng nhập hoàn tất
        time.sleep(3)
        
        # Chuyển hướng đến trang security và mở hồng bao
        security_url = "https://www.13win16.com/home/<USER>"
        print(f"🧧 Đang chuyển hướng đến trang security...")
        auto_login.driver.get(security_url)
        
        # Thực hiện mở hồng bao
        print("🧧 Đang thực hiện mở hồng bao...")
        if auto_login.open_red_envelope():
            print("✅ Đã mở hồng bao thành công!")
        else:
            print("❌ Không thể mở hồng bao")
        
        # Chờ để xem kết quả
        input("\nNhấn Enter để đóng trình duyệt...")
        
    except Exception as e:
        print(f"❌ Lỗi: {str(e)}")
    
    finally:
        # Đóng trình duyệt
        try:
            auto_login.close_driver()
            print("Đã đóng trình duyệt")
        except:
            pass

if __name__ == "__main__":
    test_red_envelope()
