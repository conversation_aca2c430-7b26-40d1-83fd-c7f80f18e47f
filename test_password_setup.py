#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script cho tính năng thiết lập mật khẩu rút tiền
Kiểm tra logic nhập mật khẩu vào 6 ô hoặc 12 ô
"""

import time
from auto_login import AutoLogin
import config

def test_password_setup():
    """Test tính năng thiết lập mật khẩu rút tiền"""
    
    print("🧪 Test tính năng thiết lập mật khẩu rút tiền")
    print("=" * 50)
    
    # Thông tin test
    username = "hoangans39"
    password = "Hoangan123"
    withdrawal_password = "121212"
    
    print(f"👤 Username: {username}")
    print(f"🔐 Password: {password}")
    print(f"💰 Withdrawal Password: {withdrawal_password}")
    print()
    
    # Khởi tạo AutoLogin
    auto_login = AutoLogin()
    
    try:
        # Thiết lập driver
        print("🔧 Bước 0: Thi<PERSON><PERSON> lập driver...")
        auto_login.setup_driver(headless=False)

        # Đăng nhập
        print("🔑 Bước 1: Đăng nhập...")
        if not auto_login.perform_login(username, password, remember_me=True):
            print("❌ Đăng nhập thất bại!")
            return False
        
        print("✅ Đăng nhập thành công!")
        print()
        
        # Chuyển đến trang security
        print("🔗 Bước 2: Chuyển đến trang security...")
        security_url = "https://www.13win16.com/home/<USER>"
        print(f"🧧 Đang chuyển hướng đến trang security...")
        auto_login.driver.get(security_url)
        time.sleep(5)
        print()
        
        # Test thiết lập mật khẩu trực tiếp
        print("🔐 Bước 3: Test thiết lập mật khẩu rút tiền...")
        print("ℹ️ Logic mới:")
        print("  - Tự động phát hiện số lượng ô input (6 hoặc 12)")
        print("  - Click vào ô đầu tiên để focus")
        print("  - Nhập từng ký tự vào đúng vị trí")
        print("  - Tìm và click nút 'Tiếp Theo'")
        print()
        
        start_time = time.time()
        
        if auto_login.setup_withdrawal_password(withdrawal_password):
            elapsed_time = int(time.time() - start_time)
            print(f"✅ Thiết lập mật khẩu thành công trong {elapsed_time}s!")
        else:
            print("❌ Có lỗi xảy ra khi thiết lập mật khẩu")
        
        print()
        print("📸 Screenshots đã tạo:")
        print("  - withdrawal_password_page.png")
        print("  - after_enter_password.png")
        print("  - after_confirm_password.png")
        print("  - withdrawal_password_error.png (nếu có lỗi)")
        
        # Chờ để xem kết quả
        input("\nNhấn Enter để đóng trình duyệt...")
        
        return True
        
    except Exception as e:
        print(f"❌ Lỗi trong quá trình test: {str(e)}")
        return False
        
    finally:
        # Đóng trình duyệt
        try:
            auto_login.close_driver()
            print("🔒 Đã đóng trình duyệt")
        except:
            pass

if __name__ == "__main__":
    print("🚀 Bắt đầu test tính năng thiết lập mật khẩu rút tiền...")
    print()
    
    success = test_password_setup()
    
    print()
    print("=" * 50)
    if success:
        print("✅ Test hoàn tất!")
    else:
        print("❌ Test thất bại!")
    
    print("🔍 Kiểm tra screenshots trong thư mục 'screenshots' để debug")
    print("📝 Kiểm tra logs để xem chi tiết quá trình thực hiện")
