# 🚀 Tool Tự Động 13win.com

Tool tự động hóa đăng nhập, liên kết ngân hàng và mở hồng bao cho 13win.com với giao diện đồ họa thân thiện.

## ✨ Tính năng chính

- 🔐 **Tự động đăng nhập** vào tài khoản 13win.com
- 🏦 **Liên kết ngân hàng** tự động với 10+ ngân hàng hỗ trợ
- 🧧 **Mở hồng bao tự động** và thiết lập mật khẩu rút tiền
- 🖥️ **Giao diện đồ họa** thân thiện, dễ sử dụng
- 💾 **L<PERSON><PERSON> c<PERSON>u hình** để sử dụng lại
- 👻 **Chế độ headless** (chạy ẩn trình duyệt)
- 📸 **Chụp màn hình** và theo dõi log chi tiết

## 🎯 Cách sử dụng

### **Chạy tool chính (Khuyến nghị):**
```bash
python main.py
```

### **Hoặc chạy trực tiếp auto login:**
```bash
python auto_login.py
```

## 🏦 Ngân hàng hỗ trợ

- Vietcombank, Techcombank, BIDV
- VietinBank, Agribank, Sacombank  
- MB Bank, ACB, VPBank, TPBank

## ⚙️ Yêu cầu hệ thống

- **Python 3.7+**
- **Chrome browser** (phiên bản mới nhất)
- **Windows 10/11** (đã test)

## 🔧 Cài đặt

1. **Tải tool về máy**
2. **Cài đặt dependencies:**
   ```bash
   pip install -r requirements.txt
   ```
3. **Đảm bảo có file `chromedriver.exe`** trong thư mục

## 📋 Hướng dẫn sử dụng GUI

1. **Nhập thông tin đăng nhập:**
   - Tên đăng nhập và mật khẩu 13win.com
   - Mật khẩu rút tiền (mặc định: 121212)

2. **Cấu hình tùy chọn:**
   - ☑️ Ghi nhớ đăng nhập
   - ☑️ Chạy ẩn trình duyệt

3. **Click "Đăng Nhập"** - Tool sẽ tự động:
   - Đăng nhập vào tài khoản
   - Tìm và mở hồng bao (nếu có)
   - Thiết lập mật khẩu rút tiền

4. **Liên kết ngân hàng (tùy chọn):**
   - Chọn ngân hàng từ danh sách
   - Nhập số tài khoản và tên chủ tài khoản
   - Click "Liên Kết Ngân Hàng"

## 📁 Cấu trúc project

```
📦 tool-13win/
├── 📄 main.py              # GUI tool chính (CHẠY FILE NÀY)
├── 📄 auto_login.py        # Logic đăng nhập và mở hồng bao  
├── 📄 bank_linking.py      # Logic liên kết ngân hàng
├── 📄 config.py            # Cấu hình tool
├── 📄 requirements.txt     # Dependencies
├── 📄 chromedriver.exe     # Chrome driver
├── 📁 data/               # Lưu cấu hình
├── 📁 screenshots/        # Ảnh chụp màn hình
└── 📁 logs/              # File log
```

## 🚀 Tính năng nâng cao

- **TURBO MODE**: Tăng tốc tối đa, tắt visual indicators
- **Visual Indicators**: Hiển thị vị trí elements bằng đường viền màu
- **Smart Input**: Nhập mật khẩu với 3 phương pháp backup
- **Auto Screenshot**: Tự động chụp màn hình các bước quan trọng
- **Error Recovery**: Tự động retry khi gặp lỗi

## ⚠️ Lưu ý quan trọng

- ✅ Tool **hoàn toàn miễn phí** và mã nguồn mở
- ✅ **Không lưu trữ** thông tin đăng nhập
- ✅ Chỉ **tự động hóa** các thao tác thủ công
- ⚠️ Sử dụng **có trách nhiệm** và tuân thủ ToS
- ⚠️ Chỉ dành cho **mục đích học tập**

## 🆘 Hỗ trợ

Nếu gặp lỗi:
1. Kiểm tra file log trong thư mục `logs/`
2. Xem ảnh screenshot trong `screenshots/`
3. Đảm bảo Chrome và ChromeDriver cùng phiên bản
