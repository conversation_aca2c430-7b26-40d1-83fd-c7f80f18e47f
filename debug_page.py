"""
Script debug để phân tích trang đăng nhập 13win.com
"""

import time
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
import config

def debug_login_page():
    """Debug trang đăng nhập"""
    driver = None
    try:
        print("=== DEBUG TRANG ĐĂNG NHẬP 13WIN.COM ===")
        
        # Thiết lập Chrome options
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        # Không dùng headless để có thể xem trang
        
        # Khởi tạo driver
        service = Service("./chromedriver.exe")
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        print("✅ Đã khởi tạo Chrome driver")
        
        # Truy cập trang đăng nhập
        print(f"Đang truy cập: {config.LOGIN_URL}")
        driver.get(config.LOGIN_URL)
        
        # Chờ trang load
        time.sleep(5)
        
        print(f"✅ Đã truy cập trang. Title: {driver.title}")
        print(f"URL hiện tại: {driver.current_url}")
        
        # Lấy HTML source
        page_source = driver.page_source
        print(f"Độ dài HTML: {len(page_source)} ký tự")
        
        # Lưu HTML để phân tích
        with open("page_source.html", "w", encoding="utf-8") as f:
            f.write(page_source)
        print("✅ Đã lưu HTML source vào page_source.html")
        
        # Phân tích các input elements
        print("\n=== PHÂN TÍCH INPUT ELEMENTS ===")
        inputs = driver.find_elements(By.TAG_NAME, "input")
        print(f"Tổng số input: {len(inputs)}")
        
        for i, inp in enumerate(inputs):
            try:
                input_type = inp.get_attribute("type") or "text"
                name = inp.get_attribute("name") or ""
                id_attr = inp.get_attribute("id") or ""
                placeholder = inp.get_attribute("placeholder") or ""
                value = inp.get_attribute("value") or ""
                class_attr = inp.get_attribute("class") or ""
                
                print(f"\nInput {i}:")
                print(f"  Type: {input_type}")
                print(f"  Name: {name}")
                print(f"  ID: {id_attr}")
                print(f"  Placeholder: {placeholder}")
                print(f"  Value: {value}")
                print(f"  Class: {class_attr}")
                print(f"  Visible: {inp.is_displayed()}")
                print(f"  Enabled: {inp.is_enabled()}")
                
            except Exception as e:
                print(f"  Lỗi khi lấy thông tin input {i}: {e}")
        
        # Phân tích các button elements
        print("\n=== PHÂN TÍCH BUTTON ELEMENTS ===")
        buttons = driver.find_elements(By.TAG_NAME, "button")
        print(f"Tổng số button: {len(buttons)}")
        
        for i, btn in enumerate(buttons):
            try:
                text = btn.text or ""
                type_attr = btn.get_attribute("type") or ""
                class_attr = btn.get_attribute("class") or ""
                onclick = btn.get_attribute("onclick") or ""
                
                print(f"\nButton {i}:")
                print(f"  Text: '{text}'")
                print(f"  Type: {type_attr}")
                print(f"  Class: {class_attr}")
                print(f"  OnClick: {onclick}")
                print(f"  Visible: {btn.is_displayed()}")
                print(f"  Enabled: {btn.is_enabled()}")
                
            except Exception as e:
                print(f"  Lỗi khi lấy thông tin button {i}: {e}")
        
        # Tìm các element có text "ĐĂNG NHẬP"
        print("\n=== TÌM ELEMENT CÓ TEXT 'ĐĂNG NHẬP' ===")
        try:
            login_elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'ĐĂNG NHẬP')]")
            print(f"Tìm thấy {len(login_elements)} element có text 'ĐĂNG NHẬP'")
            
            for i, elem in enumerate(login_elements):
                try:
                    tag_name = elem.tag_name
                    text = elem.text
                    class_attr = elem.get_attribute("class") or ""
                    
                    print(f"\nElement {i}:")
                    print(f"  Tag: {tag_name}")
                    print(f"  Text: '{text}'")
                    print(f"  Class: {class_attr}")
                    print(f"  Visible: {elem.is_displayed()}")
                    print(f"  Clickable: {elem.is_enabled()}")
                    
                except Exception as e:
                    print(f"  Lỗi khi lấy thông tin element {i}: {e}")
                    
        except Exception as e:
            print(f"Lỗi khi tìm element ĐĂNG NHẬP: {e}")
        
        # Tìm form elements
        print("\n=== PHÂN TÍCH FORM ELEMENTS ===")
        forms = driver.find_elements(By.TAG_NAME, "form")
        print(f"Tổng số form: {len(forms)}")
        
        for i, form in enumerate(forms):
            try:
                action = form.get_attribute("action") or ""
                method = form.get_attribute("method") or ""
                class_attr = form.get_attribute("class") or ""
                
                print(f"\nForm {i}:")
                print(f"  Action: {action}")
                print(f"  Method: {method}")
                print(f"  Class: {class_attr}")
                
                # Tìm input trong form này
                form_inputs = form.find_elements(By.TAG_NAME, "input")
                print(f"  Số input trong form: {len(form_inputs)}")
                
            except Exception as e:
                print(f"  Lỗi khi lấy thông tin form {i}: {e}")
        
        # Chụp màn hình
        screenshot_path = "debug_screenshot.png"
        driver.save_screenshot(screenshot_path)
        print(f"\n✅ Đã chụp màn hình: {screenshot_path}")
        
        # Chờ để có thể quan sát
        print("\n⏳ Chờ 30 giây để quan sát trang web...")
        print("Bạn có thể tương tác với trang web trong thời gian này")
        time.sleep(30)
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        
    finally:
        if driver:
            driver.quit()
            print("✅ Đã đóng trình duyệt")

if __name__ == "__main__":
    debug_login_page()
