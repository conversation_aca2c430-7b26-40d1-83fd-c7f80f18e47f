"""
Module xử lý tự động đăng nhập vào 13win.com
"""

import time
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import config

class AutoLogin:
    def __init__(self):
        self.driver = None
        self.wait = None
        
    def setup_driver(self, headless=False):
        """Thiết lập Chrome driver"""
        try:
            chrome_options = Options()
            
            # Thêm các options từ config
            for option in config.CHROME_OPTIONS:
                chrome_options.add_argument(option)
                
            if headless:
                chrome_options.add_argument("--headless")
                
            # Tắt thông báo automation
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # Thiết lập service
            service = Service(ChromeDriverManager().install())
            
            # Khởi tạo driver
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # Thiết lập timeouts
            self.driver.implicitly_wait(config.IMPLICIT_WAIT)
            self.driver.set_page_load_timeout(config.PAGE_LOAD_TIMEOUT)
            
            # Thiết lập WebDriverWait
            self.wait = WebDriverWait(self.driver, config.ELEMENT_WAIT_TIMEOUT)
            
            return True
            
        except Exception as e:
            print(f"Lỗi thiết lập driver: {str(e)}")
            return False
    
    def navigate_to_login(self):
        """Điều hướng đến trang đăng nhập"""
        try:
            print("Đang truy cập trang đăng nhập...")
            self.driver.get(config.LOGIN_URL)
            
            # Chờ trang load
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            time.sleep(2)
            
            return True
            
        except TimeoutException:
            print("Timeout khi load trang đăng nhập")
            return False
        except Exception as e:
            print(f"Lỗi khi truy cập trang: {str(e)}")
            return False
    
    def find_login_elements(self):
        """Tìm các element đăng nhập"""
        elements = {}
        
        try:
            # Tìm input username
            username_selectors = [
                "input[placeholder*='user']",
                "input[name*='user']",
                "input[id*='user']",
                "input[type='text']"
            ]
            
            for selector in username_selectors:
                try:
                    elements['username'] = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except NoSuchElementException:
                    continue
            
            # Tìm input password
            password_selectors = [
                "input[type='password']",
                "input[placeholder*='password']",
                "input[name*='pass']"
            ]
            
            for selector in password_selectors:
                try:
                    elements['password'] = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except NoSuchElementException:
                    continue
            
            # Tìm button đăng nhập
            login_selectors = [
                "button:contains('ĐĂNG NHẬP')",
                "input[type='submit']",
                "button[type='submit']",
                ".login-btn",
                ".btn-login"
            ]
            
            for selector in login_selectors:
                try:
                    if "contains" in selector:
                        elements['login_btn'] = self.driver.find_element(By.XPATH, f"//button[contains(text(), 'ĐĂNG NHẬP')]")
                    else:
                        elements['login_btn'] = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except NoSuchElementException:
                    continue
            
            return elements
            
        except Exception as e:
            print(f"Lỗi khi tìm elements: {str(e)}")
            return {}
    
    def perform_login(self, username, password, remember_me=True):
        """Thực hiện đăng nhập"""
        try:
            print("Đang thực hiện đăng nhập...")
            
            # Tìm các elements
            elements = self.find_login_elements()
            
            if not elements.get('username') or not elements.get('password'):
                print("Không tìm thấy form đăng nhập")
                return False
            
            # Nhập username
            username_input = elements['username']
            username_input.clear()
            username_input.send_keys(username)
            time.sleep(1)
            
            # Nhập password
            password_input = elements['password']
            password_input.clear()
            password_input.send_keys(password)
            time.sleep(1)
            
            # Tích checkbox ghi nhớ nếu có
            if remember_me:
                try:
                    checkbox = self.driver.find_element(By.CSS_SELECTOR, "input[type='checkbox']")
                    if not checkbox.is_selected():
                        checkbox.click()
                except NoSuchElementException:
                    pass
            
            # Click đăng nhập
            if elements.get('login_btn'):
                elements['login_btn'].click()
            else:
                # Thử submit form
                password_input.submit()
            
            # Chờ và kiểm tra kết quả
            time.sleep(3)
            
            # Kiểm tra đăng nhập thành công
            current_url = self.driver.current_url
            if "login" not in current_url.lower() or self.check_login_success():
                print("Đăng nhập thành công!")
                return True
            else:
                print("Đăng nhập thất bại")
                return False
                
        except Exception as e:
            print(f"Lỗi khi đăng nhập: {str(e)}")
            return False
    
    def check_login_success(self):
        """Kiểm tra đăng nhập thành công"""
        try:
            # Tìm các dấu hiệu đăng nhập thành công
            success_indicators = [
                ".user-info",
                ".profile",
                ".dashboard",
                ".logout",
                "[href*='logout']"
            ]
            
            for indicator in success_indicators:
                try:
                    self.driver.find_element(By.CSS_SELECTOR, indicator)
                    return True
                except NoSuchElementException:
                    continue
                    
            return False
            
        except Exception:
            return False
    
    def take_screenshot(self, filename="screenshot.png"):
        """Chụp màn hình"""
        try:
            if not os.path.exists(config.SCREENSHOTS_DIR):
                os.makedirs(config.SCREENSHOTS_DIR)
                
            filepath = os.path.join(config.SCREENSHOTS_DIR, filename)
            self.driver.save_screenshot(filepath)
            print(f"Đã lưu screenshot: {filepath}")
            return filepath
            
        except Exception as e:
            print(f"Lỗi khi chụp màn hình: {str(e)}")
            return None
    
    def close_driver(self):
        """Đóng driver"""
        try:
            if self.driver:
                self.driver.quit()
                print("Đã đóng trình duyệt")
        except Exception as e:
            print(f"Lỗi khi đóng driver: {str(e)}")
