#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script TURBO MODE - Tốc độ tối đa
Tắt visual indicators và giảm screenshots để siêu nhanh
"""

import time
from auto_login import AutoLogin
import config

def test_turbo_mode():
    """Test TURBO MODE - siêu nhanh"""
    
    print("🚀 Test TURBO MODE - Tốc độ tối đa")
    print("=" * 50)
    
    # Thông tin test
    username = "hoangans39"
    password = "Hoangan123"
    withdrawal_password = "121212"
    
    print(f"👤 Username: {username}")
    print(f"🔐 Password: {password}")
    print(f"💰 Withdrawal Password: {withdrawal_password}")
    print(f"🚀 TURBO MODE: {config.TURBO_MODE}")
    print()
    
    if config.TURBO_MODE:
        print("⚡ TURBO MODE ENABLED:")
        print("  - ❌ Tắt visual indicators")
        print("  - 📸 Chỉ screenshot quan trọng")
        print("  - ⏱️ <PERSON><PERSON><PERSON><PERSON> thời gian chờ (0.05s)")
        print("  - 🔇 Giảm logs không cần thiết")
    else:
        print("🐌 NORMAL MODE:")
        print("  - ✅ Có visual indicators")
        print("  - 📸 Tất cả screenshots")
        print("  - ⏱️ Thời gian chờ bình thường")
    
    print()
    
    # Khởi tạo AutoLogin
    auto_login = AutoLogin()
    
    try:
        # Thiết lập driver
        print("🔧 Thiết lập driver...")
        if not auto_login.setup_driver(headless=False):
            print("❌ Không thể thiết lập driver!")
            return False
        
        # Đăng nhập
        print("🔑 Đăng nhập...")
        if not auto_login.perform_login(username, password, remember_me=True):
            print("❌ Đăng nhập thất bại!")
            return False
        
        print("✅ Đăng nhập thành công!")
        
        # Chuyển đến trang security
        print("🔗 Chuyển đến trang security...")
        security_url = "https://www.13win16.com/home/<USER>"
        auto_login.driver.get(security_url)
        time.sleep(2)  # Giảm thời gian chờ
        
        # Test thiết lập mật khẩu với TURBO MODE
        print("🚀 Test TURBO MODE thiết lập mật khẩu...")
        
        start_time = time.time()
        
        if auto_login.setup_withdrawal_password(withdrawal_password):
            elapsed_time = round(time.time() - start_time, 2)
            print(f"✅ TURBO MODE hoàn thành trong {elapsed_time}s!")
            
            if config.TURBO_MODE:
                print("⚡ Tối ưu hóa TURBO:")
                print(f"  - Không visual indicators → Tiết kiệm ~2s")
                print(f"  - Ít screenshots → Tiết kiệm ~1s")
                print(f"  - Thời gian chờ ngắn → Tiết kiệm ~1s")
                print(f"  - Tổng tiết kiệm: ~4s")
            
        else:
            elapsed_time = round(time.time() - start_time, 2)
            print(f"⚠️ Có vấn đề ({elapsed_time}s)")
        
        print()
        if config.TURBO_MODE:
            print("📸 Screenshots TURBO (chỉ quan trọng):")
            print("  - withdrawal_password_page.png")
            print("  - after_confirm_password.png")
        else:
            print("📸 Screenshots đầy đủ:")
            print("  - before_red_envelope.png")
            print("  - withdrawal_password_page.png")
            print("  - after_enter_password.png")
            print("  - after_confirm_password.png")
        
        # Chờ để xem kết quả
        input("\nNhấn Enter để đóng trình duyệt...")
        
        return True
        
    except Exception as e:
        print(f"❌ Lỗi trong test: {str(e)}")
        return False
        
    finally:
        # Đóng trình duyệt
        try:
            auto_login.close_driver()
            print("🔒 Đã đóng trình duyệt")
        except:
            pass

if __name__ == "__main__":
    print("🚀 Test TURBO MODE vs NORMAL MODE...")
    print()
    
    success = test_turbo_mode()
    
    print()
    print("=" * 50)
    if success:
        print("✅ Test hoàn tất!")
        
        if config.TURBO_MODE:
            print("⚡ TURBO MODE - Siêu nhanh:")
            print("  - Không visual indicators")
            print("  - Ít screenshots")
            print("  - Thời gian chờ tối thiểu")
            print("  - Ít logs")
        else:
            print("🎨 NORMAL MODE - Đầy đủ tính năng:")
            print("  - Visual indicators đẹp")
            print("  - Screenshots chi tiết")
            print("  - Logs đầy đủ")
    else:
        print("❌ Test thất bại!")
    
    print(f"\n🔧 Để thay đổi mode, sửa TURBO_MODE trong config.py:")
    print(f"  - TURBO_MODE = True  → Siêu nhanh")
    print(f"  - TURBO_MODE = False → Đầy đủ tính năng")
