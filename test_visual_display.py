#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test visual display của password input
<PERSON><PERSON><PERSON> tra xem ký tự có hiển thị trong UI hay không
"""

import time
import config
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def test_visual_display():
    """Test visual display với nhiều phương pháp"""
    print("🚀 Test visual display với cải tiến mới...")
    
    # Setup Chrome driver
    chrome_options = Options()
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument("--disable-web-security")
    chrome_options.add_argument("--allow-running-insecure-content")
    
    driver = webdriver.Chrome(options=chrome_options)
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    try:
        print("🌐 Đang mở trang web...")
        driver.get("https://www.13win16.com/")
        time.sleep(3)
        
        # Đăng nhập nhanh
        print("🔐 Đăng nhập nhanh...")
        username_input = driver.find_element(By.CSS_SELECTOR, "input[placeholder*='Nhập Số điện thoại']")
        password_input = driver.find_element(By.CSS_SELECTOR, "input[placeholder='Mật khẩu']")
        login_button = driver.find_element(By.CSS_SELECTOR, "button.ui-button.ui-button--primary")
        
        username_input.send_keys("user1220")
        password_input.send_keys("123456")
        login_button.click()
        time.sleep(5)
        
        print("✅ Đăng nhập thành công")
        
        # Chuyển đến trang thiết lập mật khẩu
        print("🔐 Chuyển đến trang thiết lập mật khẩu...")
        security_url = "https://www.13win16.com/home/<USER>"
        driver.get(security_url)
        time.sleep(5)
        
        # Tìm input fields
        print("🔍 Tìm input fields...")
        password_inputs = driver.find_elements(By.CSS_SELECTOR, "[class*='password']")
        print(f"✅ Tìm thấy {len(password_inputs)} input fields")
        
        if len(password_inputs) >= 6:
            password = "121212"
            print(f"\n🎯 Test visual display: {password}")
            print("=" * 50)
            
            for i, digit in enumerate(password):
                if i >= 6:
                    break
                    
                print(f"\n📝 Test ô {i+1}:")
                input_field = password_inputs[i]
                
                # Phương pháp 1: JavaScript với force display
                print(f"    🎯 Ô {i+1}: Nhập '{digit}'")
                try:
                    # Highlight và focus
                    driver.execute_script("""
                        arguments[0].focus();
                        arguments[0].style.backgroundColor = 'yellow';
                        arguments[0].style.border = '3px solid red';
                        arguments[0].scrollIntoView(true);
                    """, input_field)
                    time.sleep(0.5)
                    
                    # Set value với nhiều cách
                    driver.execute_script("""
                        arguments[0].value = arguments[1];
                        arguments[0].setAttribute('value', arguments[1]);
                        arguments[0].textContent = arguments[1];
                        arguments[0].innerText = arguments[1];
                        arguments[0].innerHTML = arguments[1];
                    """, input_field, digit)
                    
                    # Trigger events
                    driver.execute_script("""
                        arguments[0].dispatchEvent(new Event('input', { bubbles: true }));
                        arguments[0].dispatchEvent(new Event('change', { bubbles: true }));
                        arguments[0].dispatchEvent(new Event('keyup', { bubbles: true }));
                        arguments[0].dispatchEvent(new Event('focus', { bubbles: true }));
                    """, input_field)
                    
                    # Force visual style
                    driver.execute_script("""
                        arguments[0].style.color = 'black';
                        arguments[0].style.fontSize = '20px';
                        arguments[0].style.fontWeight = 'bold';
                        arguments[0].style.textAlign = 'center';
                        arguments[0].style.backgroundColor = 'lightgreen';
                    """, input_field)
                    
                    time.sleep(1)
                    
                    # Kiểm tra kết quả
                    current_value = input_field.get_attribute('value')
                    visible_text = input_field.text
                    inner_text = driver.execute_script("return arguments[0].innerText;", input_field)
                    
                    print(f"      ✅ Value: '{current_value}'")
                    print(f"      ✅ Text: '{visible_text}'")
                    print(f"      ✅ InnerText: '{inner_text}'")
                    
                    if current_value == digit:
                        print(f"      🎉 Thành công!")
                    else:
                        print(f"      ❌ Thất bại: expected '{digit}', got '{current_value}'")
                        
                except Exception as e:
                    print(f"      ❌ Lỗi: {e}")
                
                # Phương pháp 2: Simulation typing nếu cần
                if input_field.get_attribute('value') != digit:
                    print(f"      🔄 Thử simulation typing...")
                    try:
                        input_field.click()
                        input_field.clear()
                        
                        # Type từng ký tự
                        for char in digit:
                            input_field.send_keys(char)
                            time.sleep(0.2)
                        
                        # Force style
                        driver.execute_script("""
                            arguments[0].style.color = 'blue';
                            arguments[0].style.fontSize = '22px';
                            arguments[0].style.fontWeight = 'bold';
                            arguments[0].style.backgroundColor = 'lightblue';
                        """, input_field)
                        
                        final_value = input_field.get_attribute('value')
                        print(f"      ✅ Simulation result: '{final_value}'")
                        
                    except Exception as e:
                        print(f"      ❌ Simulation lỗi: {e}")
            
            print(f"\n🎯 Kết quả cuối cùng:")
            print("=" * 30)
            for i in range(6):
                try:
                    value = password_inputs[i].get_attribute('value') or ""
                    expected = password[i]
                    status = "✅" if value == expected else "❌"
                    print(f"  {status} Ô {i+1}: '{expected}' → '{value}'")
                except:
                    print(f"  ❌ Ô {i+1}: Lỗi đọc")
            
            print(f"\n📸 Đã chụp screenshot: test_visual_display_final.png")
            driver.save_screenshot("test_visual_display_final.png")
            
            print(f"\n⏳ Giữ trình duyệt mở 15 giây để quan sát...")
            time.sleep(15)
        
    except Exception as e:
        print(f"❌ Lỗi trong quá trình test: {e}")
        driver.save_screenshot("test_visual_display_error.png")
    
    finally:
        print("🔚 Đóng trình duyệt...")
        driver.quit()

if __name__ == "__main__":
    test_visual_display()
