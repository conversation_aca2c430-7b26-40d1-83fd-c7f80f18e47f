#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script nhanh cho tính năng thiết lập mật khẩu rút tiền
Tối ưu hóa tốc độ và xử lý lỗi "invalid element state"
"""

import time
from auto_login import AutoLogin

def test_fast_password():
    """Test nhanh thiết lập mật khẩu rút tiền"""
    
    print("⚡ Test nhanh thiết lập mật khẩu rút tiền")
    print("=" * 50)
    
    # Thông tin test
    username = "hoangans39"
    password = "Hoangan123"
    withdrawal_password = "121212"
    
    print(f"👤 Username: {username}")
    print(f"🔐 Password: {password}")
    print(f"💰 Withdrawal Password: {withdrawal_password}")
    print()
    
    # Khởi tạo AutoLogin
    auto_login = AutoLogin()
    
    try:
        # Thiết lập driver
        print("🔧 Thiết lập driver...")
        if not auto_login.setup_driver(headless=False):
            print("❌ Không thể thiết lập driver!")
            return False
        
        # Đăng nhập
        print("🔑 Đăng nhập...")
        if not auto_login.perform_login(username, password, remember_me=True):
            print("❌ Đăng nhập thất bại!")
            return False
        
        print("✅ Đăng nhập thành công!")
        
        # Chuyển đến trang security
        print("🔗 Chuyển đến trang security...")
        security_url = "https://www.13win16.com/home/<USER>"
        auto_login.driver.get(security_url)
        time.sleep(3)
        
        # Test thiết lập mật khẩu trực tiếp
        print("🔐 Test thiết lập mật khẩu nhanh...")
        print("🚀 Tối ưu hóa:")
        print("  - Chỉ highlight ô đầu tiên")
        print("  - Giảm thời gian chờ visual indicators")
        print("  - Xử lý lỗi 'invalid element state'")
        print("  - Thử nhiều cách nhập (click + JavaScript)")
        print()
        
        start_time = time.time()
        
        if auto_login.setup_withdrawal_password(withdrawal_password):
            elapsed_time = int(time.time() - start_time)
            print(f"✅ Thiết lập mật khẩu thành công trong {elapsed_time}s!")
            print("🎯 Cải tiến hoạt động tốt!")
        else:
            elapsed_time = int(time.time() - start_time)
            print(f"⚠️ Có vấn đề khi thiết lập mật khẩu ({elapsed_time}s)")
            print("🔍 Kiểm tra screenshots để debug")
        
        print()
        print("📸 Screenshots:")
        print("  - withdrawal_password_page.png")
        print("  - after_enter_password.png")
        print("  - after_confirm_password.png")
        
        # Chờ để xem kết quả
        input("\nNhấn Enter để đóng trình duyệt...")
        
        return True
        
    except Exception as e:
        print(f"❌ Lỗi trong test: {str(e)}")
        return False
        
    finally:
        # Đóng trình duyệt
        try:
            auto_login.close_driver()
            print("🔒 Đã đóng trình duyệt")
        except:
            pass

if __name__ == "__main__":
    print("🚀 Test nhanh với tối ưu hóa mới...")
    print()
    
    success = test_fast_password()
    
    print()
    print("=" * 50)
    if success:
        print("✅ Test hoàn tất!")
        print("⚡ Tối ưu hóa tốc độ:")
        print("  - Visual indicators nhanh hơn (0.5s thay vì 1-2s)")
        print("  - Chỉ highlight ô cần thiết")
        print("  - Xử lý lỗi 'invalid element state'")
        print("  - Fallback JavaScript khi click thất bại")
    else:
        print("❌ Test thất bại!")
    
    print("\n🔧 Cải tiến đã thực hiện:")
    print("  - Giảm thời gian visual indicators")
    print("  - Thử nhiều cách nhập (click + JS)")
    print("  - Continue thay vì return False khi lỗi")
    print("  - Chỉ highlight ô đầu tiên thay vì tất cả")
