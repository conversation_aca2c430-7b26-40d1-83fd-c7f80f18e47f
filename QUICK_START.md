# ⚡ Hướng Dẫn Nhanh

## 🚀 Chạy Tool

### **Cách 1: GUI Tool (Khuyến nghị)**
```bash
python main.py
```
- ✅ Giao diện đồ họa thân thiện
- ✅ Đầy đủ tính năng
- ✅ Dễ sử dụng cho người mới

### **Cách 2: Command Line**
```bash
python auto_login.py
```
- ✅ Chạy trực tiếp
- ✅ <PERSON><PERSON>h hơn
- ✅ Phù hợp với người có kinh nghiệm

## 📝 Cấu Hình Nhanh

1. **Mở file `config.py`**
2. **Sửa thông tin đăng nhập:**
   ```python
   USERNAME = "your_username"  # Thay bằng tên đăng nhập của bạn
   PASSWORD = "your_password"  # Thay bằng mật khẩu của bạn
   ```
3. **Lưu file và chạy tool**

## 🎯 Quy Trình Tự Động

Tool sẽ tự động thực hiện:

1. **🔐 Đăng nhập** vào 13win.com
2. **🧧 Tìm và mở hồng bao** (nếu có)
3. **🔑 Thiết lập mật khẩu rút tiền** (121212)
4. **📸 Chụp screenshot** các bước quan trọng
5. **📝 Ghi log** chi tiết

## ⚠️ Lưu Ý

- Đảm bảo **Chrome browser** đã cài đặt
- File **chromedriver.exe** phải có trong thư mục
- **Kết nối internet** ổn định
- **Thông tin đăng nhập** chính xác

## 🆘 Nếu Gặp Lỗi

1. Kiểm tra **screenshots/** để xem lỗi
2. Xem **logs/** để đọc chi tiết
3. Đảm bảo Chrome và ChromeDriver cùng phiên bản
