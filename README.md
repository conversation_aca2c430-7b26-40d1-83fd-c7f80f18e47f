# Tool Tự Động Đăng Nhập & Mở Hồng Bao - 13win.com

Tool tự động hóa quá trình đăng nhập, mở hồng bao và liên kết ngân hàng cho trang web 13win.com với giao diện GUI thân thiện.

## Tính năng

- ✅ Tự động đăng nhập vào 13win.com
- ✅ **Mở hồng bao tự động** sau khi đăng nhập thành công
- ✅ **Thiết lập mật khẩu rút tiền** tự động (mặc định: 121212)
- ✅ Tự động liên kết tài khoản ngân hàng
- ✅ Giao diện GUI dễ sử dụng
- ✅ Hỗ trợ nhiều ngân hàng Việt Nam
- ✅ Chế độ chạy ẩn (headless)
- ✅ Lưu/Load cấu hình
- ✅ Chụ<PERSON> màn hình
- ✅ Nhật ký hoạt động chi tiết

## Ngân hàng được hỗ trợ

- Vietcombank
- Techcombank
- BIDV
- VietinBank
- Agribank
- Sacombank
- MB Bank
- ACB
- VPBank
- TPBank

## Yêu cầu hệ thống

- Python 3.7+
- Windows/Linux/MacOS
- Chrome browser
- Kết nối internet

## Cài đặt

### 1. Clone hoặc tải về project

```bash
git clone <repository-url>
cd tool-add-ngan-hang
```

### 2. Cài đặt dependencies

```bash
pip install -r requirements.txt
```

### 3. Chạy ứng dụng

```bash
python main.py
```

## Hướng dẫn sử dụng

### 1. Đăng nhập

1. Nhập **tên đăng nhập** và **mật khẩu** của bạn
2. Nhập **mật khẩu rút tiền** (mặc định: 121212)
3. Chọn **"Ghi nhớ đăng nhập"** nếu muốn
4. Chọn **"Chạy ẩn"** nếu không muốn hiển thị trình duyệt
5. Click **"Đăng Nhập"**

### 2. Mở hồng bao 🧧

1. Sau khi đăng nhập thành công, click **"🧧 Mở Hồng Bao"**
2. Tool sẽ tự động:
   - Chuyển hướng đến trang security
   - **Tìm hồng bao trong 30 giây** (không phải tài khoản nào cũng có)
   - Nếu có hồng bao: click nút "MỞ" → đóng popup
   - **Tự động thiết lập mật khẩu rút tiền** (121212)
   - Click "Xác Nhận"
3. **Lưu ý**: Nếu không tìm thấy hồng bao trong 30s, tool sẽ tự động bỏ qua
4. Kiểm tra log để xem kết quả chi tiết

### 3. Liên kết ngân hàng

1. Sau khi đăng nhập thành công, chọn **ngân hàng** từ dropdown
2. Nhập **số tài khoản**
3. Nhập **tên tài khoản** (chính xác như trên thẻ ATM)
4. Nhập **chi nhánh** (tùy chọn)
5. Click **"Liên Kết Ngân Hàng"**

### 4. Các chức năng khác

- **Kiểm tra ngân hàng đã liên kết**: Xem danh sách ngân hàng đã được liên kết
- **Chụp màn hình**: Lưu ảnh màn hình hiện tại
- **Lưu cấu hình**: Lưu thông tin để sử dụng lần sau
- **Đóng trình duyệt**: Đóng session hiện tại

## Cấu trúc project

```
tool-add-ngan-hang/
├── main.py              # File chính - GUI
├── auto_login.py        # Module đăng nhập tự động
├── bank_linking.py      # Module liên kết ngân hàng
├── config.py           # File cấu hình
├── requirements.txt    # Dependencies
├── README.md          # Hướng dẫn này
├── data/              # Thư mục lưu cấu hình
├── screenshots/       # Thư mục lưu ảnh chụp màn hình
└── logs/             # Thư mục lưu log
```

## Screenshots tự động

Tool sẽ tự động chụp màn hình tại các bước quan trọng:

- `before_red_envelope.png` - Trước khi mở hồng bao
- `after_open_envelope.png` - Sau khi click nút MỞ
- `after_close_popup.png` - Sau khi đóng popup
- `withdrawal_password_page.png` - Trang thiết lập mật khẩu
- `after_enter_password.png` - Sau khi nhập mật khẩu
- `after_confirm_password.png` - Sau khi xác nhận

## Cấu hình

Bạn có thể tùy chỉnh cấu hình trong file `config.py`:

- URL trang web
- Timeout settings
- CSS selectors
- Chrome options
- Danh sách ngân hàng hỗ trợ
- Mật khẩu rút tiền mặc định

## Xử lý lỗi

### Lỗi thường gặp:

1. **"Không thể khởi tạo trình duyệt"**
   - Kiểm tra Chrome đã được cài đặt
   - Cập nhật Chrome lên phiên bản mới nhất

2. **"Không tìm thấy form đăng nhập"**
   - Trang web có thể đã thay đổi giao diện
   - Kiểm tra kết nối internet

3. **"Đăng nhập thất bại"**
   - Kiểm tra tên đăng nhập và mật khẩu
   - Thử đăng nhập thủ công trước

4. **"Không tìm thấy phần liên kết ngân hàng"**
   - Đảm bảo đã đăng nhập thành công
   - Trang web có thể đã thay đổi cấu trúc

## Bảo mật

- ⚠️ **Không chia sẻ thông tin đăng nhập**
- ⚠️ **Chỉ sử dụng trên máy tính cá nhân**
- ⚠️ **Không lưu mật khẩu trong file cấu hình**
- ⚠️ **Kiểm tra kỹ thông tin ngân hàng trước khi submit**

## Lưu ý quan trọng

1. Tool này chỉ dành cho mục đích tự động hóa cá nhân
2. Người dùng chịu trách nhiệm về việc sử dụng tool
3. Không sử dụng cho mục đích bất hợp pháp
4. Luôn kiểm tra kỹ thông tin trước khi thực hiện

## Hỗ trợ

Nếu gặp vấn đề, vui lòng:

1. Kiểm tra log trong ứng dụng
2. Chụp màn hình lỗi
3. Kiểm tra file log trong thư mục `logs/`

## Cập nhật

Tool sẽ được cập nhật thường xuyên để tương thích với các thay đổi của trang web.

---

**Lưu ý**: Tool này được phát triển cho mục đích học tập và tự động hóa cá nhân. Vui lòng sử dụng có trách nhiệm.
