"""
Module xử lý tự động đăng nhập vào 13win.com
"""

import time
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import config

class AutoLogin:
    def __init__(self):
        self.driver = None
        self.wait = None
        
    def setup_driver(self, headless=False):
        """Thiết lập Chrome driver"""
        try:
            chrome_options = Options()

            # Thêm các options từ config
            for option in config.CHROME_OPTIONS:
                chrome_options.add_argument(option)

            if headless:
                chrome_options.add_argument("--headless")

            # Tắt thông báo automation
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # Thiết lập service với xử lý lỗi Windows
            try:
                # Thử sử dụng driver local trước
                local_driver = "./chromedriver.exe"
                if os.path.exists(local_driver):
                    print(f"Sử dụng Chrome driver local: {local_driver}")
                    service = Service(local_driver)
                else:
                    # Thử cài đặt driver mới
                    driver_path = ChromeDriverManager().install()
                    print(f"Driver path: {driver_path}")
                    service = Service(driver_path)
            except Exception as driver_error:
                print(f"Lỗi tải driver: {driver_error}")
                # Thử sử dụng driver có sẵn trong PATH
                try:
                    service = Service()  # Sử dụng driver trong PATH
                    print("Sử dụng Chrome driver từ PATH")
                except Exception:
                    # Thử tải lại driver với cache clear
                    import shutil
                    cache_dir = os.path.join(os.path.expanduser("~"), ".wdm")
                    if os.path.exists(cache_dir):
                        shutil.rmtree(cache_dir)
                        print("Đã xóa cache driver, thử tải lại...")
                    driver_path = ChromeDriverManager().install()
                    service = Service(driver_path)

            # Khởi tạo driver
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            # Thiết lập timeouts
            self.driver.implicitly_wait(config.IMPLICIT_WAIT)
            self.driver.set_page_load_timeout(config.PAGE_LOAD_TIMEOUT)

            # Thiết lập WebDriverWait
            self.wait = WebDriverWait(self.driver, config.ELEMENT_WAIT_TIMEOUT)

            # Mở trang login để test driver
            print("🌐 Đang mở trang đăng nhập...")
            self.driver.get(config.LOGIN_URL)
            time.sleep(3)

            # Kiểm tra trang đã load thành công
            title = self.driver.title
            print(f"✅ Đã khởi tạo Chrome driver thành công - Title: {title[:50]}")
            return True

        except Exception as e:
            print(f"Lỗi thiết lập driver: {str(e)}")
            print("Hướng dẫn khắc phục:")
            print("1. Đảm bảo Chrome browser đã được cài đặt")
            print("2. Cập nhật Chrome lên phiên bản mới nhất")
            print("3. Thử chạy lại với quyền Administrator")
            print("4. Kiểm tra antivirus có chặn không")
            return False
    
    def navigate_to_login(self):
        """Điều hướng đến trang đăng nhập"""
        try:
            print("Đang truy cập trang đăng nhập...")
            self.driver.get(config.LOGIN_URL)
            
            # Chờ trang load
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            time.sleep(2)
            
            return True
            
        except TimeoutException:
            print("Timeout khi load trang đăng nhập")
            return False
        except Exception as e:
            print(f"Lỗi khi truy cập trang: {str(e)}")
            return False
    
    def check_driver_alive(self):
        """Kiểm tra driver còn hoạt động không"""
        try:
            if not self.driver:
                return False
            # Thử lấy title để kiểm tra driver còn hoạt động
            _ = self.driver.title
            return True
        except Exception:
            return False

    def find_login_elements(self):
        """Tìm các element đăng nhập"""
        elements = {}

        try:
            # Kiểm tra driver còn hoạt động không
            if not self.check_driver_alive():
                print("❌ Driver không hoạt động, thử khởi tạo lại...")
                if not self.setup_driver():
                    print("❌ Không thể khởi tạo lại driver")
                    return {}

                # Điều hướng đến trang login
                if not self.navigate_to_login():
                    print("❌ Không thể điều hướng đến trang login")
                    return {}

            print("Đang tìm các element đăng nhập...")

            # Chờ trang load hoàn toàn
            time.sleep(3)

            # Tìm input username - dựa trên debug, có placeholder "Nhập Số điện thoại/Tên Đăng Nhập"
            username_selectors = [
                "input[placeholder*='Nhập Số điện thoại']",
                "input[placeholder*='Tên Đăng Nhập']",
                "input.ui-input__input:first-of-type",
                "input[type='text']:first-of-type",
                "input[type='text']"
            ]

            for selector in username_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    print(f"Tìm thấy username input với selector: {selector}")
                    elements['username'] = element
                    break
                except NoSuchElementException:
                    continue

            # Tìm input password - dựa trên debug, có placeholder "Mật khẩu" và type="text"
            password_selectors = [
                "input[placeholder='Mật khẩu']",
                "input[placeholder*='Mật khẩu']",
                "input.ui-input__input:nth-of-type(2)",
                "input[type='text']:nth-of-type(2)",
                "input[type='password']"
            ]

            for selector in password_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    # Kiểm tra không phải là username input
                    placeholder = element.get_attribute('placeholder') or ''
                    if 'Mật khẩu' in placeholder or selector.endswith(':nth-of-type(2)'):
                        print(f"Tìm thấy password input với selector: {selector}")
                        elements['password'] = element
                        break
                except NoSuchElementException:
                    continue

            # Tìm button đăng nhập - dựa trên debug, có text "ĐĂNG NHẬP" và class "ui-button"
            login_selectors = [
                "button.ui-button.ui-button--primary",
                "//button[contains(text(), 'ĐĂNG NHẬP')]",
                "button[class*='ui-button']",
                ".ui-button--primary",
                "button[type='button']"
            ]

            for selector in login_selectors:
                try:
                    if selector.startswith("//"):
                        element = self.driver.find_element(By.XPATH, selector)
                    else:
                        element = self.driver.find_element(By.CSS_SELECTOR, selector)

                    # Kiểm tra text của button
                    button_text = element.text or ''
                    if 'ĐĂNG NHẬP' in button_text or 'ui-button' in element.get_attribute('class'):
                        print(f"Tìm thấy login button với selector: {selector}")
                        elements['login_btn'] = element
                        break
                except NoSuchElementException:
                    continue

            # Tìm checkbox "Ghi nhớ tài khoản mật khẩu" - dựa trên debug, có class "ui-checkbox__input"
            try:
                checkbox_selectors = [
                    "input.ui-checkbox__input",
                    "input[type='checkbox']"
                ]

                for selector in checkbox_selectors:
                    try:
                        checkbox = self.driver.find_element(By.CSS_SELECTOR, selector)
                        elements['remember_checkbox'] = checkbox
                        print(f"Tìm thấy remember checkbox với selector: {selector}")
                        break
                    except NoSuchElementException:
                        continue
                else:
                    print("Không tìm thấy remember checkbox")

            except Exception as e:
                print(f"Lỗi khi tìm checkbox: {e}")

            # Debug: In ra tất cả input elements
            all_inputs = self.driver.find_elements(By.TAG_NAME, "input")
            print(f"Tổng số input elements: {len(all_inputs)}")
            for i, inp in enumerate(all_inputs):
                try:
                    print(f"Input {i}: type={inp.get_attribute('type')}, name={inp.get_attribute('name')}, placeholder={inp.get_attribute('placeholder')}")
                except:
                    pass

            # Debug: In ra tất cả button elements
            all_buttons = self.driver.find_elements(By.TAG_NAME, "button")
            print(f"Tổng số button elements: {len(all_buttons)}")
            for i, btn in enumerate(all_buttons):
                try:
                    print(f"Button {i}: text={btn.text}, class={btn.get_attribute('class')}")
                except:
                    pass

            return elements

        except Exception as e:
            print(f"Lỗi khi tìm elements: {str(e)}")
            return {}
    
    def perform_login(self, username, password, remember_me=True):
        """Thực hiện đăng nhập"""
        try:
            print("Đang thực hiện đăng nhập...")

            # Tìm các elements
            elements = self.find_login_elements()

            if not elements.get('username'):
                print("❌ Không tìm thấy input username")
                return False

            if not elements.get('password'):
                print("❌ Không tìm thấy input password")
                return False

            print("✅ Đã tìm thấy form đăng nhập")

            # Nhập username
            print(f"Đang nhập username: {username}")
            username_input = elements['username']

            # Xóa nội dung cũ và nhập mới
            username_input.clear()
            time.sleep(0.5)
            username_input.send_keys(username)
            time.sleep(1)

            # Kiểm tra đã nhập thành công chưa
            current_value = username_input.get_attribute('value')
            print(f"Username đã nhập: {current_value}")

            # Nhập password
            print("Đang nhập password...")
            password_input = elements['password']
            password_input.clear()
            time.sleep(0.5)
            password_input.send_keys(password)
            time.sleep(1)

            # Tích checkbox ghi nhớ nếu có
            if remember_me and elements.get('remember_checkbox'):
                try:
                    checkbox = elements['remember_checkbox']
                    if not checkbox.is_selected():
                        print("Đang tích checkbox ghi nhớ...")
                        checkbox.click()
                        time.sleep(0.5)
                except Exception as e:
                    print(f"Lỗi khi tích checkbox: {e}")

            # Chụp màn hình trước khi click đăng nhập
            self.take_screenshot("before_login.png")

            # Click đăng nhập
            if elements.get('login_btn'):
                print("Đang click nút đăng nhập...")
                login_btn = elements['login_btn']

                # Scroll đến button nếu cần
                self.driver.execute_script("arguments[0].scrollIntoView(true);", login_btn)
                time.sleep(1)

                # Click button
                login_btn.click()
                print("Đã click nút đăng nhập")
            else:
                print("Không tìm thấy nút đăng nhập, thử submit form...")
                password_input.submit()

            # Chờ và kiểm tra kết quả
            print("Đang chờ kết quả đăng nhập...")
            time.sleep(5)

            # Chụp màn hình sau khi đăng nhập
            self.take_screenshot("after_login.png")

            # Kiểm tra đăng nhập thành công
            current_url = self.driver.current_url
            print(f"URL hiện tại: {current_url}")

            if "login" not in current_url.lower() or self.check_login_success():
                print("✅ Đăng nhập thành công!")

                # Chuyển hướng đến trang security để mở hồng bao
                security_url = "https://www.13win16.com/home/<USER>"
                print(f"Đang chuyển hướng đến trang security: {security_url}")
                self.driver.get(security_url)
                time.sleep(3)

                # Thực hiện mở hồng bao
                if self.open_red_envelope():
                    print("✅ Đã mở hồng bao thành công!")
                else:
                    print("❌ Không thể mở hồng bao")

                return True
            else:
                print("❌ Đăng nhập thất bại - vẫn ở trang login")

                # Kiểm tra có thông báo lỗi không
                try:
                    error_selectors = [
                        ".error",
                        ".alert-danger",
                        ".message-error",
                        "*[class*='error']",
                        "*[style*='color: red']"
                    ]

                    for selector in error_selectors:
                        try:
                            error_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                            if error_element.is_displayed():
                                print(f"Thông báo lỗi: {error_element.text}")
                        except:
                            continue

                except Exception:
                    pass

                return False

        except Exception as e:
            print(f"Lỗi khi đăng nhập: {str(e)}")
            self.take_screenshot("login_error.png")
            return False
    
    def check_login_success(self):
        """Kiểm tra đăng nhập thành công"""
        try:
            # Tìm các dấu hiệu đăng nhập thành công
            success_indicators = [
                ".user-info",
                ".profile",
                ".dashboard",
                ".logout",
                "[href*='logout']"
            ]
            
            for indicator in success_indicators:
                try:
                    self.driver.find_element(By.CSS_SELECTOR, indicator)
                    return True
                except NoSuchElementException:
                    continue
                    
            return False
            
        except Exception:
            return False

    def open_red_envelope(self, withdrawal_password=None):
        """Mở hồng bao"""
        try:
            print("🧧 Đang tìm hồng bao...")

            # Sử dụng mật khẩu mặc định nếu không được cung cấp
            if withdrawal_password is None:
                withdrawal_password = config.DEFAULT_WITHDRAWAL_PASSWORD

            # Chờ trang load
            time.sleep(5)

            # Chụp màn hình trước khi mở hồng bao
            self.take_screenshot("before_red_envelope.png")

            # Tìm hồng bao với timeout từ config
            timeout = config.RED_ENVELOPE_TIMEOUT
            print(f"🧧 Đang tìm hồng bao trong {timeout} giây...")
            start_time = time.time()

            found_red_envelope = False

            # Tìm nút "MỞ" trên hồng bao với timeout
            open_button_selectors = [
                "//a[contains(text(), 'MỞ')]",  # Thêm selector cho thẻ a
                "//button[contains(text(), 'MỞ')]",
                "//div[contains(text(), 'MỞ')]",
                "//span[contains(text(), 'MỞ')]",
                ".redpocket-collet-btn",  # Class cụ thể cho nút hồng bao
                ".open-button",
                "[class*='open']",
                "[class*='redpocket']",  # Class chứa redpocket
                "button[class*='envelope']",
                "*[onclick*='open']"
            ]

            open_button = None

            # Lặp tìm kiếm trong 30 giây
            while time.time() - start_time < timeout and not found_red_envelope:
                for selector in open_button_selectors:
                    try:
                        if selector.startswith("//"):
                            element = self.driver.find_element(By.XPATH, selector)
                        else:
                            element = self.driver.find_element(By.CSS_SELECTOR, selector)

                        if element.is_displayed() and element.is_enabled():
                            print(f"✅ Tìm thấy nút MỞ với selector: {selector}")
                            # Thêm visual indicator cho nút MỞ
                            self.add_visual_indicators(element, "red", "OPEN")
                            time.sleep(0.5)  # Giảm thời gian chờ
                            open_button = element
                            found_red_envelope = True
                            break
                    except NoSuchElementException:
                        continue

                if not found_red_envelope:
                    # Chờ 2 giây trước khi thử lại
                    time.sleep(2)
                    elapsed_time = int(time.time() - start_time)
                    print(f"⏳ Đã tìm {elapsed_time}/{timeout}s - Chưa thấy hồng bao...")

            if not open_button:
                elapsed_time = int(time.time() - start_time)
                print(f"⏰ Đã hết thời gian chờ ({elapsed_time}s) - Không tìm thấy hồng bao")
                print("ℹ️ Có thể tài khoản này không có hồng bao hoặc đã được mở")

                # Vẫn thực hiện thiết lập mật khẩu rút tiền nếu có trang đó
                time.sleep(3)
                if self.setup_withdrawal_password(withdrawal_password):
                    print("✅ Đã thiết lập mật khẩu rút tiền thành công!")

                return True  # Trả về True vì không có lỗi, chỉ là không có hồng bao

            # Click nút MỞ
            print("🧧 Đang click nút MỞ...")
            self.driver.execute_script("arguments[0].scrollIntoView(true);", open_button)
            time.sleep(1)
            open_button.click()

            # Chờ hồng bao mở
            time.sleep(3)

            # Chụp màn hình sau khi mở hồng bao
            self.take_screenshot("after_open_envelope.png")

            # Tìm và click dấu X để đóng popup (tối ưu hóa)
            print("❌ Đang tìm nút đóng (X)...")

            # Selector tối ưu cho nút X dựa trên hình ảnh
            close_button_selectors = [
                # Selector cho nút X trong popup hồng bao (dựa trên hình ảnh)
                "//div[contains(@style, 'position') and contains(@style, 'absolute')]//span[text()='×']",
                "//div[contains(@class, 'popup') or contains(@class, 'modal')]//span[text()='×']",
                "//span[text()='×' and contains(@style, 'cursor')]",
                "//div[@role='button']//span[text()='×']",
                "//span[text()='×']",
                "//button[text()='×']",
                "//div[text()='×']",
                # Selector chung
                "//div[contains(@class, 'close') or contains(@class, 'x-btn')]//span[text()='×']",
                "//button[contains(@class, 'close')]",
                "//div[contains(@class, 'close')]",
                "[class*='close']",
                "[class*='x-btn']",
                "[class*='popup-close']",
                "button[aria-label*='close']",
                "*[onclick*='close']"
            ]

            close_button = None
            # Tìm kiếm nhanh trong 10 giây
            close_start_time = time.time()
            close_timeout = 10

            while time.time() - close_start_time < close_timeout and not close_button:
                for selector in close_button_selectors:
                    try:
                        if selector.startswith("//"):
                            element = self.driver.find_element(By.XPATH, selector)
                        else:
                            element = self.driver.find_element(By.CSS_SELECTOR, selector)

                        if element.is_displayed() and element.is_enabled():
                            print(f"✅ Tìm thấy nút đóng với selector: {selector}")
                            # Thêm visual indicator cho nút X
                            self.add_visual_indicators(element, "red", "CLOSE")
                            time.sleep(0.5)  # Giảm thời gian chờ
                            close_button = element
                            break
                    except NoSuchElementException:
                        continue

                if not close_button:
                    time.sleep(0.5)  # Chờ ngắn hơn để tăng tốc

            if close_button:
                print("❌ Đang click nút đóng...")
                # Click nhanh và chắc chắn
                self.driver.execute_script("arguments[0].click();", close_button)
                time.sleep(1)  # Giảm thời gian chờ

                # Chụp màn hình sau khi đóng popup
                self.take_screenshot("after_close_popup.png")

                print("✅ Đã đóng popup thành công!")
                print("🔐 Chuyển đến trang thiết lập mật khẩu rút tiền...")

                # Chờ ngắn hơn vì chắc chắn sẽ chuyển đến trang thiết lập mật khẩu
                time.sleep(2)
                if self.setup_withdrawal_password(withdrawal_password):
                    print("✅ Đã thiết lập mật khẩu rút tiền thành công!")
            else:
                print("⚠️ Không tìm thấy nút đóng trong 10s")

                # Debug: Tìm tất cả element có thể là nút đóng
                print("🔍 Debug: Tìm kiếm tất cả element có thể là nút đóng...")
                try:
                    # Tìm tất cả element chứa ×
                    x_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), '×') or contains(text(), 'X') or contains(text(), '✕')]")
                    print(f"Tìm thấy {len(x_elements)} element chứa ×:")

                    for i, elem in enumerate(x_elements[:5]):  # Chỉ in 5 element đầu
                        try:
                            tag = elem.tag_name
                            text = elem.text[:20] if elem.text else ""
                            class_attr = elem.get_attribute("class") or ""
                            style = elem.get_attribute("style") or ""
                            print(f"  {i}: {tag} - '{text}' - class: {class_attr[:50]} - style: {style[:50]}")
                        except:
                            pass

                    # Thử click element đầu tiên nếu có
                    if x_elements:
                        print("🎯 Thử click element đầu tiên...")
                        self.driver.execute_script("arguments[0].click();", x_elements[0])
                        time.sleep(2)
                        self.take_screenshot("after_force_close.png")

                except Exception as debug_e:
                    print(f"Debug error: {debug_e}")

                # Vẫn thử thiết lập mật khẩu rút tiền
                time.sleep(3)
                if self.setup_withdrawal_password(withdrawal_password):
                    print("✅ Đã thiết lập mật khẩu rút tiền thành công!")

            return True

        except Exception as e:
            print(f"❌ Lỗi khi mở hồng bao: {str(e)}")
            self.take_screenshot("red_envelope_error.png")
            return False

    def setup_withdrawal_password(self, password):
        """Thiết lập mật khẩu rút tiền"""
        try:
            print("🔐 Đang kiểm tra trang thiết lập mật khẩu rút tiền...")

            # Chụp màn hình hiện tại
            self.take_screenshot("withdrawal_password_page.png")

            # Kiểm tra xem có phải trang thiết lập mật khẩu rút tiền không
            page_indicators = [
                "//div[contains(text(), 'Sửa Đổi Mật Khẩu Rút Tiền')]",
                "//div[contains(text(), 'Xác Nhận Mật Khẩu Mới')]",
                "//h1[contains(text(), 'Thiết Lập Mật Khẩu Rút Tiền')]",
                "//div[contains(text(), 'Thiết Lập Mật Khẩu Rút Tiền')]",
                "//span[contains(text(), 'Thiết Lập Mật Khẩu Rút Tiền')]",
                "//title[contains(text(), 'Thiết Lập Mật Khẩu')]",
                # Thêm các indicator mới dựa trên giao diện
                "//div[contains(text(), 'Mật khẩu rút tiền không thể trống')]",
                "//button[contains(text(), 'Tiếp Theo')]",
                "//div[contains(text(), 'Lưu ý: Mật khẩu rút tiền')]",
                # Thêm selector linh hoạt hơn
                "//*[contains(text(), 'mật khẩu rút tiền')]",
                "//*[contains(text(), 'Mật Khẩu Rút Tiền')]",
                "//*[contains(text(), 'withdrawal password')]",
                "//*[contains(text(), 'PIN')]",
                "//*[contains(text(), 'pin')]"
            ]

            is_password_page = False
            for indicator in page_indicators:
                try:
                    element = self.driver.find_element(By.XPATH, indicator)
                    is_password_page = True
                    print(f"✅ Đã tìm thấy trang thiết lập mật khẩu rút tiền: {element.text[:50]}")
                    # Thêm visual indicator cho element được tìm thấy
                    self.add_visual_indicators(element, "blue", "FOUND")
                    break
                except NoSuchElementException:
                    continue

            if not is_password_page:
                print("⚠️ Không phát hiện được trang thiết lập mật khẩu rút tiền")

                # Debug: Tìm tất cả text có chứa "mật khẩu"
                print("🔍 Debug: Tìm kiếm text chứa 'mật khẩu'...")
                try:
                    password_texts = self.driver.find_elements(By.XPATH, "//*[contains(text(), 'mật khẩu') or contains(text(), 'Mật Khẩu') or contains(text(), 'password')]")
                    print(f"Tìm thấy {len(password_texts)} element chứa 'mật khẩu':")
                    for i, elem in enumerate(password_texts[:5]):
                        try:
                            text = elem.text[:50] if elem.text else ""
                            tag = elem.tag_name
                            print(f"  {i}: {tag} - '{text}'")
                            # Thêm visual indicator cho text tìm được
                            if i == 0:  # Highlight element đầu tiên
                                self.add_visual_indicators(elem, "orange", "FOUND")
                        except:
                            pass
                except Exception as debug_e:
                    print(f"Debug error: {debug_e}")

                print("🔄 Vẫn thử tìm ô input để thiết lập mật khẩu...")
                # Không return False, tiếp tục thử tìm input fields

            # Tìm các ô nhập mật khẩu (có thể là 6 ô hoặc 12 ô)
            password_input_selectors = [
                "input[type='password']",
                "input[type='text'][maxlength='1']",
                "input[maxlength='1']",
                "input[placeholder*='mật khẩu']",
                "input[placeholder*='password']",
                ".password-input",
                "[class*='password']",
                "[class*='pin']",
                "input[inputmode='numeric']",
                # Thêm selector tổng quát hơn
                "input[type='text']",
                "input[type='number']",
                "input"
            ]

            password_inputs = []
            for selector in password_input_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            password_inputs.append(element)

                    # Nếu đã tìm thấy ít nhất 6 input, dừng lại
                    if len(password_inputs) >= 6:
                        print(f"✅ Tìm thấy đủ {len(password_inputs)} ô input với selector: {selector}")
                        break
                except:
                    continue

            if not password_inputs:
                print("❌ Không tìm thấy ô nhập mật khẩu")

                # Debug: Tìm tất cả input fields
                print("🔍 Debug: Tìm tất cả input fields...")
                try:
                    all_inputs = self.driver.find_elements(By.TAG_NAME, "input")
                    print(f"Tìm thấy {len(all_inputs)} input fields:")
                    for i, inp in enumerate(all_inputs[:10]):
                        try:
                            input_type = inp.get_attribute("type") or "text"
                            placeholder = inp.get_attribute("placeholder") or ""
                            class_attr = inp.get_attribute("class") or ""
                            maxlength = inp.get_attribute("maxlength") or ""
                            visible = inp.is_displayed()
                            enabled = inp.is_enabled()
                            print(f"  {i}: type={input_type}, placeholder='{placeholder[:20]}', class='{class_attr[:20]}', maxlength={maxlength}, visible={visible}, enabled={enabled}")

                            # Highlight tất cả input fields
                            if visible and enabled:
                                self.add_visual_indicators(inp, "yellow", f"INPUT{i}")
                        except:
                            pass
                except Exception as debug_e:
                    print(f"Debug error: {debug_e}")

                return False

            print(f"✅ Tìm thấy {len(password_inputs)} ô nhập mật khẩu")

            # Chỉ highlight ô đầu tiên để tăng tốc (trừ khi TURBO_MODE)
            first_input = password_inputs[0]
            if not config.TURBO_MODE:
                self.add_visual_indicators(first_input, "green", "FIRST")

            # Click vào ô đầu tiên để focus
            if not config.TURBO_MODE:
                print("🎯 Click vào ô đầu tiên để bắt đầu nhập...")

            try:
                self.driver.execute_script("arguments[0].scrollIntoView(true);", first_input)

                # Giảm thời gian chờ trong TURBO_MODE
                wait_time = 0.1 if config.TURBO_MODE else 0.3
                time.sleep(wait_time)

                # Thử nhiều cách click
                try:
                    first_input.click()
                except:
                    self.driver.execute_script("arguments[0].click();", first_input)

                time.sleep(wait_time)
                if not config.TURBO_MODE:
                    print("✅ Đã click vào ô đầu tiên")
            except Exception as e:
                if not config.TURBO_MODE:
                    print(f"⚠️ Không thể click ô đầu tiên: {e}")

            # Xác định cách nhập mật khẩu
            if len(password_inputs) >= 12:
                # Có 12 ô: 6 cho "Sửa Đổi" + 6 cho "Xác Nhận"
                print(f"🔢 Nhập mật khẩu vào 6 ô đầu tiên (Sửa Đổi): {password}")

                # Nhập vào 6 ô đầu tiên
                for i, digit in enumerate(password):
                    if i < 6:
                        try:
                            input_field = password_inputs[i]

                            # Thử nhiều cách nhập - ưu tiên JavaScript
                            success = False

                            # Cách 1: JavaScript (ưu tiên vì hoạt động tốt nhất)
                            try:
                                # Highlight element để visual feedback
                                self.driver.execute_script("""
                                    arguments[0].focus();
                                    arguments[0].style.backgroundColor = 'lightblue';
                                    arguments[0].style.border = '2px solid blue';
                                """, input_field)
                                time.sleep(0.2)

                                # Set value và trigger events
                                self.driver.execute_script("arguments[0].value = arguments[1];", input_field, digit)
                                self.driver.execute_script("arguments[0].dispatchEvent(new Event('input', { bubbles: true }));", input_field)
                                self.driver.execute_script("arguments[0].dispatchEvent(new Event('change', { bubbles: true }));", input_field)
                                self.driver.execute_script("arguments[0].dispatchEvent(new Event('keyup', { bubbles: true }));", input_field)

                                # Kiểm tra thành công
                                current_value = input_field.get_attribute('value')
                                if current_value == digit:
                                    success = True
                                    # Highlight thành công
                                    self.driver.execute_script("""
                                        arguments[0].style.backgroundColor = 'lightgreen';
                                        arguments[0].style.border = '2px solid green';
                                    """, input_field)
                            except:
                                pass

                            # Cách 2: Click + Send keys nếu JavaScript thất bại
                            if not success:
                                try:
                                    input_field.click()
                                    time.sleep(0.1)
                                    input_field.clear()
                                    input_field.send_keys(digit)

                                    # Kiểm tra
                                    current_value = input_field.get_attribute('value')
                                    if current_value == digit:
                                        success = True
                                except:
                                    pass

                            # Cách 3: Send keys trực tiếp nếu vẫn thất bại
                            if not success:
                                try:
                                    input_field.send_keys(digit)
                                    success = True
                                except:
                                    pass

                            # Cách 4: Force visual update nếu vẫn thất bại
                            if not success:
                                try:
                                    # Simulate typing với delay
                                    input_field.click()
                                    time.sleep(0.1)
                                    input_field.clear()
                                    for char in digit:
                                        input_field.send_keys(char)
                                        time.sleep(0.05)

                                    # Force UI refresh
                                    self.driver.execute_script("arguments[0].blur(); arguments[0].focus();", input_field)
                                    success = True
                                except:
                                    pass

                            # Kiểm tra cuối cùng và hiển thị giá trị thực tế
                            final_value = input_field.get_attribute('value') or ""

                            # Nếu vẫn không hiển thị, thử phương pháp cuối cùng
                            if final_value != digit:
                                try:
                                    # Force focus và highlight
                                    self.driver.execute_script("""
                                        arguments[0].focus();
                                        arguments[0].select();
                                        arguments[0].style.backgroundColor = 'yellow';
                                        arguments[0].style.border = '2px solid red';
                                    """, input_field)
                                    time.sleep(0.5)

                                    # Thử nhập lại với simulation thật
                                    input_field.clear()
                                    input_field.click()
                                    input_field.send_keys(digit)

                                    # Kiểm tra lại
                                    final_value = input_field.get_attribute('value') or ""
                                except:
                                    pass

                            # Giảm thời gian chờ trong TURBO_MODE
                            wait_time = 0.05 if config.TURBO_MODE else 0.2
                            time.sleep(wait_time)

                            if not config.TURBO_MODE:
                                status = "✅" if final_value == digit else "❌"
                                print(f"  {status} Ô {i+1}: Nhập '{digit}' → Hiển thị '{final_value}'")

                                # Debug: Kiểm tra visual state
                                if final_value != digit:
                                    try:
                                        is_visible = input_field.is_displayed()
                                        is_enabled = input_field.is_enabled()
                                        element_text = input_field.text
                                        print(f"    🔍 Debug: visible={is_visible}, enabled={is_enabled}, text='{element_text}'")
                                    except:
                                        pass
                        except Exception as e:
                            print(f"⚠️ Lỗi khi nhập ký tự {i+1}: {e}, thử tiếp...")
                            continue  # Không return False, tiếp tục với ô tiếp theo

                print(f"🔢 Nhập mật khẩu vào 6 ô tiếp theo (Xác Nhận): {password}")

                # Debug: Kiểm tra các ô xác nhận (7-12)
                print("🔍 Debug các ô xác nhận:")
                for i in range(6):
                    try:
                        element = password_inputs[i + 6]
                        is_visible = element.is_displayed()
                        is_enabled = element.is_enabled()
                        current_value = element.get_attribute('value') or ""
                        print(f"  Ô {i+7}: visible={is_visible}, enabled={is_enabled}, value='{current_value}'")
                    except Exception as e:
                        print(f"  Ô {i+7}: Lỗi - {e}")

                # Nhập vào 6 ô tiếp theo
                for i, digit in enumerate(password):
                    if i < 6:
                        try:
                            input_field = password_inputs[i + 6]

                            # Thử nhiều cách nhập - ưu tiên JavaScript
                            success = False

                            # Cách 1: JavaScript (ưu tiên vì hoạt động tốt nhất)
                            try:
                                # Highlight element để visual feedback
                                self.driver.execute_script("""
                                    arguments[0].focus();
                                    arguments[0].style.backgroundColor = 'lightblue';
                                    arguments[0].style.border = '2px solid blue';
                                """, input_field)
                                time.sleep(0.2)

                                # Set value và trigger events
                                self.driver.execute_script("arguments[0].value = arguments[1];", input_field, digit)
                                self.driver.execute_script("arguments[0].dispatchEvent(new Event('input', { bubbles: true }));", input_field)
                                self.driver.execute_script("arguments[0].dispatchEvent(new Event('change', { bubbles: true }));", input_field)
                                self.driver.execute_script("arguments[0].dispatchEvent(new Event('keyup', { bubbles: true }));", input_field)

                                # Kiểm tra thành công
                                current_value = input_field.get_attribute('value')
                                if current_value == digit:
                                    success = True
                                    # Highlight thành công
                                    self.driver.execute_script("""
                                        arguments[0].style.backgroundColor = 'lightgreen';
                                        arguments[0].style.border = '2px solid green';
                                    """, input_field)
                            except:
                                pass

                            # Cách 2: Click + Send keys nếu JavaScript thất bại
                            if not success:
                                try:
                                    input_field.click()
                                    time.sleep(0.1)
                                    input_field.clear()
                                    input_field.send_keys(digit)

                                    # Kiểm tra
                                    current_value = input_field.get_attribute('value')
                                    if current_value == digit:
                                        success = True
                                except:
                                    pass

                            # Cách 3: Send keys trực tiếp nếu vẫn thất bại
                            if not success:
                                try:
                                    input_field.send_keys(digit)
                                    success = True
                                except:
                                    pass

                            # Cách 4: Force visual update nếu vẫn thất bại
                            if not success:
                                try:
                                    # Simulate typing với delay
                                    input_field.click()
                                    time.sleep(0.1)
                                    input_field.clear()
                                    for char in digit:
                                        input_field.send_keys(char)
                                        time.sleep(0.05)

                                    # Force UI refresh
                                    self.driver.execute_script("arguments[0].blur(); arguments[0].focus();", input_field)
                                    success = True
                                except:
                                    pass

                            # Kiểm tra cuối cùng và hiển thị giá trị thực tế
                            final_value = input_field.get_attribute('value') or ""

                            time.sleep(0.2)
                            status = "✅" if final_value == digit else "❌"
                            print(f"  {status} Ô xác nhận {i+1}: Nhập '{digit}' → Hiển thị '{final_value}'")
                        except Exception as e:
                            print(f"⚠️ Lỗi khi nhập ký tự xác nhận {i+1}: {e}, thử tiếp...")
                            continue

            elif len(password_inputs) >= 6:
                # Có 6 ô: nhập từng ký tự
                print(f"🔢 Nhập mật khẩu vào 6 ô: {password}")

                for i, digit in enumerate(password):
                    if i < 6:
                        try:
                            input_field = password_inputs[i]

                            # Thử nhiều cách nhập để đảm bảo hiển thị
                            success = False

                            # Cách 1: Click + Clear + Send keys
                            try:
                                input_field.click()
                                time.sleep(0.1)
                                input_field.clear()
                                input_field.send_keys(digit)

                                # Kiểm tra đã nhập thành công chưa
                                current_value = input_field.get_attribute('value')
                                if current_value == digit:
                                    success = True
                            except:
                                pass

                            # Cách 2: JavaScript nếu cách 1 thất bại
                            if not success:
                                try:
                                    self.driver.execute_script("arguments[0].focus();", input_field)
                                    self.driver.execute_script("arguments[0].value = arguments[1];", input_field, digit)
                                    # Trigger nhiều events để UI cập nhật
                                    self.driver.execute_script("arguments[0].dispatchEvent(new Event('input', { bubbles: true }));", input_field)
                                    self.driver.execute_script("arguments[0].dispatchEvent(new Event('change', { bubbles: true }));", input_field)
                                    self.driver.execute_script("arguments[0].dispatchEvent(new Event('keyup', { bubbles: true }));", input_field)
                                    self.driver.execute_script("arguments[0].dispatchEvent(new Event('blur', { bubbles: true }));", input_field)

                                    # Kiểm tra lại
                                    current_value = input_field.get_attribute('value')
                                    if current_value == digit:
                                        success = True
                                except:
                                    pass

                            # Cách 3: Send keys trực tiếp nếu vẫn thất bại
                            if not success:
                                try:
                                    input_field.send_keys(digit)
                                    success = True
                                except:
                                    pass

                            # Cách 4: Force visual update nếu vẫn thất bại
                            if not success:
                                try:
                                    # Simulate typing với delay
                                    input_field.click()
                                    time.sleep(0.1)
                                    input_field.clear()
                                    for char in digit:
                                        input_field.send_keys(char)
                                        time.sleep(0.05)

                                    # Force UI refresh
                                    self.driver.execute_script("arguments[0].blur(); arguments[0].focus();", input_field)
                                    success = True
                                except:
                                    pass

                            # Kiểm tra cuối cùng và hiển thị giá trị thực tế
                            final_value = input_field.get_attribute('value') or ""

                            time.sleep(0.2)
                            status = "✅" if final_value == digit else "❌"
                            print(f"  {status} Ô {i+1}: Nhập '{digit}' → Hiển thị '{final_value}'")
                        except Exception as e:
                            print(f"⚠️ Lỗi khi nhập ký tự {i+1}: {e}, thử tiếp...")
                            continue
            else:
                # Ít hơn 6 ô: nhập toàn bộ mật khẩu vào từng ô
                print(f"🔢 Nhập toàn bộ mật khẩu vào {len(password_inputs)} ô")

                for i, input_field in enumerate(password_inputs):
                    try:
                        print(f"🔐 Đang nhập mật khẩu vào ô {i+1}...")
                        input_field.clear()
                        input_field.send_keys(password)
                        time.sleep(0.5)
                    except Exception as e:
                        print(f"⚠️ Lỗi khi nhập mật khẩu vào ô {i+1}: {str(e)}")

            # Chụp màn hình sau khi nhập mật khẩu
            self.take_screenshot("after_enter_password.png")

            # Tìm và click nút "Tiếp Theo"
            confirm_button_selectors = [
                "//button[contains(text(), 'Tiếp Theo')]",
                "//div[contains(text(), 'Tiếp Theo')]",
                "//span[contains(text(), 'Tiếp Theo')]",
                "//button[contains(text(), 'Xác Nhận')]",
                "//button[contains(text(), 'XÁC NHẬN')]",
                "//button[contains(text(), 'Confirm')]",
                "//div[contains(text(), 'Xác Nhận')]",
                ".confirm-btn",
                ".submit-btn",
                ".next-btn",
                "button[type='submit']",
                "[class*='confirm']",
                "[class*='next']"
            ]

            confirm_button = None
            for selector in confirm_button_selectors:
                try:
                    if selector.startswith("//"):
                        element = self.driver.find_element(By.XPATH, selector)
                    else:
                        element = self.driver.find_element(By.CSS_SELECTOR, selector)

                    if element.is_displayed() and element.is_enabled():
                        print(f"✅ Tìm thấy nút xác nhận với selector: {selector}")
                        # Thêm visual indicator cho nút "Tiếp Theo"
                        self.add_visual_indicators(element, "green", "NEXT")
                        time.sleep(0.5)  # Giảm thời gian chờ
                        confirm_button = element
                        break
                except NoSuchElementException:
                    continue

            if not confirm_button:
                print("❌ Không tìm thấy nút 'Tiếp Theo'")

                # Debug: In ra tất cả các button
                print("🔍 Debug: Tìm kiếm tất cả button...")
                buttons = self.driver.find_elements(By.TAG_NAME, "button")
                print(f"Tìm thấy {len(buttons)} button:")
                for i, btn in enumerate(buttons[:10]):  # Chỉ in 10 button đầu
                    try:
                        text = btn.text[:30] if btn.text else ""
                        class_attr = btn.get_attribute("class") or ""
                        onclick = btn.get_attribute("onclick") or ""
                        print(f"  {i}: '{text}' - class: {class_attr[:50]} - onclick: {onclick[:30]}")
                    except:
                        pass

                # Debug: Tìm tất cả div có thể click
                clickable_divs = self.driver.find_elements(By.XPATH, "//div[contains(@onclick, '') or contains(@class, 'btn') or contains(@class, 'button')]")
                print(f"Tìm thấy {len(clickable_divs)} div có thể click:")
                for i, div in enumerate(clickable_divs[:5]):
                    try:
                        text = div.text[:30] if div.text else ""
                        class_attr = div.get_attribute("class") or ""
                        print(f"  {i}: '{text}' - class: {class_attr[:50]}")
                    except:
                        pass

                return False

            # Click nút "Tiếp Theo"
            print("✅ Đang click nút 'Tiếp Theo'...")
            self.driver.execute_script("arguments[0].scrollIntoView(true);", confirm_button)
            time.sleep(1)
            self.driver.execute_script("arguments[0].click();", confirm_button)

            # Chờ và chụp màn hình kết quả
            time.sleep(3)
            self.take_screenshot("after_confirm_password.png")

            print("✅ Đã hoàn thành thiết lập mật khẩu rút tiền!")
            return True

        except Exception as e:
            print(f"❌ Lỗi khi thiết lập mật khẩu rút tiền: {str(e)}")
            self.take_screenshot("withdrawal_password_error.png")
            return False

    def add_visual_indicators(self, elements, color="red", label="TARGET"):
        """Thêm visual indicators (line đỏ) để xác định vị trí elements"""
        try:
            # Kiểm tra TURBO_MODE - bỏ qua visual indicators để tăng tốc
            if config.TURBO_MODE:
                return

            if not elements:
                return

            # JavaScript để vẽ line đỏ xung quanh elements
            js_code = """
            var elements = arguments[0];
            var color = arguments[1];
            var label = arguments[2];

            if (!Array.isArray(elements)) {
                elements = [elements];
            }

            elements.forEach(function(element, index) {
                if (element) {
                    // Tạo border đỏ
                    element.style.border = '3px solid ' + color;
                    element.style.boxShadow = '0 0 10px ' + color;

                    // Tạo label
                    var labelDiv = document.createElement('div');
                    labelDiv.innerHTML = label + ' ' + (index + 1);
                    labelDiv.style.position = 'absolute';
                    labelDiv.style.backgroundColor = color;
                    labelDiv.style.color = 'white';
                    labelDiv.style.padding = '2px 5px';
                    labelDiv.style.fontSize = '12px';
                    labelDiv.style.fontWeight = 'bold';
                    labelDiv.style.zIndex = '9999';
                    labelDiv.style.top = (element.offsetTop - 20) + 'px';
                    labelDiv.style.left = element.offsetLeft + 'px';

                    // Thêm label vào trang
                    document.body.appendChild(labelDiv);

                    // Scroll để hiển thị element
                    element.scrollIntoView({behavior: 'smooth', block: 'center'});
                }
            });
            """

            if isinstance(elements, list):
                self.driver.execute_script(js_code, elements, color, label)
            else:
                self.driver.execute_script(js_code, [elements], color, label)

            print(f"✅ Đã thêm visual indicators ({color}) cho {len(elements) if isinstance(elements, list) else 1} element(s)")

        except Exception as e:
            print(f"⚠️ Lỗi khi thêm visual indicators: {e}")

    def take_screenshot(self, filename="screenshot.png"):
        """Chụp màn hình"""
        try:
            # Trong TURBO_MODE, chỉ chụp screenshot quan trọng
            if config.TURBO_MODE:
                important_screenshots = [
                    "withdrawal_password_page.png",
                    "after_confirm_password.png",
                    "withdrawal_password_error.png"
                ]
                if filename not in important_screenshots:
                    return None

            if not os.path.exists(config.SCREENSHOTS_DIR):
                os.makedirs(config.SCREENSHOTS_DIR)

            filepath = os.path.join(config.SCREENSHOTS_DIR, filename)
            self.driver.save_screenshot(filepath)

            if not config.TURBO_MODE:
                print(f"Đã lưu screenshot: {filepath}")

            return filepath

        except Exception as e:
            if not config.TURBO_MODE:
                print(f"Lỗi khi chụp màn hình: {str(e)}")
            return None
    
    def close_driver(self):
        """Đóng driver"""
        try:
            if self.driver:
                self.driver.quit()
                print("Đã đóng trình duyệt")
        except Exception as e:
            print(f"Lỗi khi đóng driver: {str(e)}")
